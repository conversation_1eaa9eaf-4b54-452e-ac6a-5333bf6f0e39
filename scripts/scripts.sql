-- report 表增加唯一键
ALTER TABLE survey.report ADD CONSTRAINT report_unique UNIQUE (corpus_id,product,activity);


-- 更新语料ID
UPDATE survey.activity_feedback af
SET corpus_id = am.corpus_id
FROM survey.report am
WHERE am.task_id = af.task_id
  AND af.corpus_id IS NULL;

-- 删除视图
DROP VIEW survey.v_report_pivot CASCADE;

CREATE TABLE survey.activity (
                                 id serial4 NOT NULL,
                                 activity_name varchar(200) NULL,
                                 activity_type varchar(10) NULL,
                                 start_time varchar(20) NULL,
                                 end_time varchar(20) NULL,
                                 activity_status varchar(10) NULL,
                                 display_window_name bool NULL,
                                 notify_type varchar(10) NULL,
                                 create_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
                                 create_user varchar(255) NOT NULL,
                                 update_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
                                 update_user varchar(255) NULL,
                                 activity_desc text NULL,
                                 person jsonb NULL,
                                 form_config varchar NULL,
                                 score_reason_config jsonb NULL,
                                 "source" varchar(20) NULL,
                                 CONSTRAINT activity_pkey PRIMARY KEY (id)
);

CREATE TABLE survey.activity_feedback (
                                          id serial4 NOT NULL,
                                          activity_id int4 NULL,
                                          window_name varchar(200) NULL,
                                          group_index int4 NULL,
                                          score float4 NULL,
                                          provider varchar(50) NULL,
                                          dept varchar(255) NULL,
                                          create_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                          score_reason jsonb NULL,
                                          custom_feedback jsonb NULL,
                                          task_id varchar(20) NULL,
                                          CONSTRAINT activity_feedback_pkey PRIMARY KEY (id)
);
CREATE TABLE survey.activity_material (
                                          id serial4 NOT NULL,
                                          activity_id int4 NULL,
                                          group_index int4 DEFAULT 1 NOT NULL,
                                          corpus_id int4 NULL,
                                          user_corpus jsonb NULL,
                                          attachment jsonb NULL,
                                          window_data jsonb NULL,
                                          CONSTRAINT activity_material_pkey PRIMARY KEY (id)
);
CREATE TABLE survey.corpus (
                               id serial4 NOT NULL,
                               feature varchar(50) NOT NULL,
                               sub_feature varchar(50) NOT NULL,
                               scene varchar(50) NULL,
                               third_scene varchar(50) NULL,
                               "level" varchar(50) NOT NULL,
                               user_corpus jsonb NOT NULL,
                               "source" varchar(50) NOT NULL,
                               create_user varchar(50) NOT NULL,
                               create_time timestamp(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
                               update_time timestamp(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
                               golden_choose varchar(255) NULL,
                               golden_result varchar(255) NULL,
                               attachment jsonb NULL,
                               secondary_scene varchar(50) NULL,
                               "type" varchar(50) NULL,
                               CONSTRAINT corpus_pkey PRIMARY KEY (id)
);

CREATE TABLE survey.report (
                               id serial4 NOT NULL,
                               corpus_id int4 NOT NULL,
                               task_id varchar(50) NOT NULL,
                               product varchar(100) NOT NULL,
                               "version" varchar(100) NOT NULL,
                               start_time timestamp NOT NULL,
                               end_time timestamp NOT NULL,
                               performance float8 NULL,
                               reliability float8 NULL,
                               "result" jsonb NOT NULL,
                               functionality jsonb NOT NULL,
                               survey_score float8 NULL,
                               create_time timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
                               task_set_name varchar(20) NULL,
                               CONSTRAINT report_pkey PRIMARY KEY (id),
                               CONSTRAINT report_unique UNIQUE (task_id)
);
CREATE TABLE survey."cache" (
                                id serial4 NOT NULL,
                                cache_key varchar(200) NOT NULL,
                                "content" jsonb NULL,
                                CONSTRAINT cache_pkey PRIMARY KEY (id),
                                CONSTRAINT cache_unique UNIQUE (cache_key)
);





#!/bin/bash

# Define the source and destination directories
SOURCE_DIR="target"
DESTINATION_DIR="build_target"  # Change this to your desired destination directory

# Run Maven clean package
echo "Running Maven clean package..."
mvn clean package

# Check if the Maven command was successful
if [ $? -ne 0 ]; then
    echo "Maven build failed. Exiting."
    exit 1
fi

# Get the current timestamp in the format YYYYMMDD_HHMMSS
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Find the JAR file in the source directory
JAR_FILE=$(find "$SOURCE_DIR" -name "*.jar" | head -n 1)

# Check if the JAR file exists
if [ -z "$JAR_FILE" ]; then
    echo "No JAR file found in $SOURCE_DIR"
    exit 1
fi

# Construct the new filename with the timestamp
BASENAME=$(basename "$JAR_FILE")
NEW_FILENAME="${BASENAME%.jar}_$TIMESTAMP.jar"

# Copy the JAR file to the destination with the new filename
cp "$JAR_FILE" "$DESTINATION_DIR/$NEW_FILENAME"

# Confirm the copy operation
if [ $? -eq 0 ]; then
    echo "Copied $JAR_FILE to $DESTINATION_DIR/$NEW_FILENAME"
else
    echo "Failed to copy $JAR_FILE"
    exit 1
fi

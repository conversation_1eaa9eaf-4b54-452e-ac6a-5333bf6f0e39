package com.huawei.ifocus.controller;

import com.huawei.ifocus.common.aop.operation.OperationLog;
import com.huawei.ifocus.common.utils.Message;
import com.huawei.ifocus.controller.request.*;
import com.huawei.ifocus.controller.response.*;
import com.huawei.ifocus.dto.CorpusProductScoreInfo;
import com.huawei.ifocus.dto.FeedbackData;
import com.huawei.ifocus.dto.ProviderScoreDto;
import com.huawei.ifocus.service.ActivityResultService;
import com.huawei.ifocus.service.ActivityService;

import com.fasterxml.jackson.databind.JsonNode;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "AI众测活动")
@Validated
@RestController
@RequestMapping("/activity")
@RequiredArgsConstructor
public class ActivityController {
    private final ActivityService activityService;
    private final ActivityResultService activityResultService;

    @Operation(summary = "判断用户是否已经参与活动")
    @GetMapping("/isDone")
    public Message<Boolean> isDone(Integer activityId) {
        return Message.success(activityService.isDone(activityId));
    }

    @OperationLog(module = "queryActivity", operate = "query")
    @Operation(summary = "获取所有活动信息")
    @GetMapping("/activities")
    public Message<ActivityViewPage> queryActivityPage(ActivityQuery query) {
        return Message.success(activityService.queryAllActivity(query));
    }

    @Operation(summary = "创建活动")
    @PostMapping("/create")
    public Message<Integer> createActivity(@RequestBody ActivityCreateReq activityCreateReq){
        return Message.success(activityService.createActivity(activityCreateReq));
    }

    @Operation(summary = "编辑活动")
    @PutMapping("/activityInfo")
    public Message<Boolean> editActivity(@RequestBody ActivityEditReq activityEditReq){
        return Message.success(activityService.editActivity(activityEditReq));
    }

    @Operation(summary = "查询活动信息")
    @GetMapping("/activityInfo")
    public Message<ActivityInfoView> getActivityInfo(@RequestParam Integer id){
        return Message.success(activityService.getActivityInfo(id));
    }

    @Operation(summary = "删除活动")
    @DeleteMapping("/activity")
    public Message<Boolean> deleteActivity(@RequestParam Integer id){
        return Message.success(activityService.deleteActivity(id));
    }

    @Operation(summary = "查看活动进展")
    @GetMapping("/activityResult")
    public Message<ActivityResultView> activityResult(@RequestParam Integer id){
        return Message.success(activityResultService.queryActivityResult(id));
    }

    @Operation(summary = "查看问卷详情")
    @PostMapping("/activityFeedbackDetail")
    public Message<FeedBackDetailView> feedBackDetail(@RequestBody FeedBackDetailRequest feedBackDetailRequest){
        return Message.success(activityResultService.queryFeedBackDetail(feedBackDetailRequest));
    }


    @Operation(summary = "提交活动测评结果")
    @PostMapping("/activityFeedback")
    public Message<Boolean> activityFeedback(@RequestBody ActivityFeedbackReq activityFeedbackReq){
        return Message.success(activityResultService.saveActivityFeedback(activityFeedbackReq));
    }

    @Operation(summary = "获取活动评测详情")
    @GetMapping("/activityFeedback")
    public Message<List<FeedbackData>> getActivityFeedback(@RequestParam Integer id){
        return Message.success(activityResultService.getActivityFeedback(id));
    }

    @Operation(summary = "评分细则统计")
    @PostMapping("/activityFeedbackReport")
    public Message<SurveyStaticsView> activityFeedbackReport(@RequestParam Integer activityId){
        return Message.success(activityResultService.queryFeedbackStatics(activityId));
    }


    @Operation(summary = "检查活动是否有人提交")
    @GetMapping("/checkActivityHasSubmit")
    public Message<Boolean> checkActivityHasSubmit(@RequestParam Integer activityId){
        return Message.success(!activityResultService.checkActivityHasSubmit(activityId));
    }


    @Operation(summary = "暂存评分")
    @PostMapping("/saveScoreCache")
    public Message<Boolean> saveScoreCache(@RequestBody ScoreCacheRequest scoreCacheRequest){
        return Message.success(activityService.saveScoreCache(scoreCacheRequest));
    }

    @Operation(summary = "查询评分缓存")
    @PostMapping("/queryScoreCache")
    public Message<JsonNode> queryScoreCache(@RequestBody ScoreCacheRequest scoreCacheRequest){
        return Message.success(activityService.queryScoreCache(scoreCacheRequest));
    }

    @Operation(summary = "删除缓存评分")
    @PostMapping("/deleteScoreCache")
    public Message<Boolean> deleteScoreCache(@RequestBody ScoreCacheRequest scoreCacheRequest){
        return Message.success(activityService.deleteScoreCache(scoreCacheRequest));
    }


    @Operation(summary = "语料维度打分详情统计")
    @PostMapping("/corpusProductScoreStatics")
    public Message<List<CorpusProductScoreInfo>> corpusProductScoreStatics(@RequestParam Integer activityId){
        return Message.success(activityResultService.queryCorpusProductScoreStatics(activityId));
    }


    @Operation(summary = "查询活动语料所有用户打分")
    @PostMapping("/queryAllUserCorpusScore")
    public Message<List<ProviderScoreDto>> queryAllUserCorpusScore(@RequestParam Integer activityId, @RequestParam Integer corpusId,@RequestParam  String product){
        return Message.success(activityResultService.queryAllUserCorpusScore(activityId,corpusId,product));
    }


}

package com.huawei.ifocus.controller;

import com.huawei.ifocus.common.utils.Message;
import com.huawei.ifocus.controller.request.*;
import com.huawei.ifocus.controller.response.ActivityView;
import com.huawei.ifocus.controller.response.ReportStaticsView;
import com.huawei.ifocus.dto.*;
import com.huawei.ifocus.service.ReportService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;

import org.springframework.data.domain.Page;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Tag(name = "测试报告")
@RestController
@RequestMapping("/report")
@RequiredArgsConstructor
public class ReportController {

    private final ReportService reportService;

    @Operation(summary = "报告列表查询")
    @PostMapping("/query")
    public Message<Page<ReportInfo>> getReportPage(@RequestBody ReportQuery query){
        return Message.success(reportService.queryReportInfoPage(query));
    }

    @Operation(summary = "报告删除")
    @DeleteMapping("/delete")
    public Message<List<Integer>> delReportById(@RequestBody List<Integer> ids){
        return Message.success(reportService.delReport(ids));
    }

    @Operation(summary = "报告导出")
    @PostMapping("/export")
    public void export(@RequestBody ReportQuery query, HttpServletResponse response) throws IOException {
        reportService.export(query,response);
    }

    @Operation(summary = "报告统计")
    @PostMapping("/stats")
    public Message<ReportStaticsView> stats(@Validated  @RequestBody StatsQuery query) {
        return Message.success(reportService.queryReportStatistics(query));
    }

    @Operation(summary = "查询众测详情")
    @PostMapping("/querySurveyActivityDetail")
    public Message<List<ActivityView>> querySurveyDetail(String taskId) {
        return Message.success(reportService.queryReportSurveyDetail(taskId));
    }

    @Operation(summary = "产品列表")
    @GetMapping("/productList")
    public Message productList(@RequestParam String activity,@RequestParam(required = false) String feature) {
        return Message.success(reportService.queryProductList(activity,feature));
    }

    @Operation(summary = "活动列表")
    @GetMapping("/activityList")
    public Message activityList() {
        return Message.success(reportService.queryActivityList());
    }

    @Operation(summary = "特性列表")
    @GetMapping("/featureList")
    public Message featureList(@RequestParam String activity) {
        return Message.success(reportService.queryFeatureList(activity));
    }

    @Operation(summary = "单活动内语料打分详情导出")
    @GetMapping("/corpusScoreDetailExport")
    public void corpusScoreDetailExport(HttpServletResponse response,Integer id) throws Exception{
          reportService.corpusScoreDetailExport(response,id);
    }


    @Operation(summary = "语料聚合统计")
    @PostMapping("/corpusStats")
    public Message<Page<CorpusStatsInfo>> corpusStats(@Validated  @RequestBody CorpusStatsQuery query) {
        return Message.success(reportService.queryCorpusStats(query));
    }

    @Operation(summary = "语料聚合导出")
    @PostMapping("/corpusStatsExport")
    public void corpusStatsExport(@Validated  @RequestBody CorpusStatsQuery query, HttpServletResponse response) throws IOException {
        reportService.corpusStatsExport(query,response);
    }

    /**
     * 语料统计 表头产品查询
     * @param query
     * @return
     */
    @PostMapping("/corpusProductList")
    public Message<List<String>> corpusProductList(@Validated  @RequestBody CorpusStatsQuery query) {
        return Message.success(reportService.corpusProductList(query));
    }

    /**
     * 查询语料对应产品打分结果
     * @param query
     * @return
     */
    @PostMapping("/corpusProductResult")
    public Message<List<CorpusProductResultInfo>> corpusProductResult(@Validated  @RequestBody CorpusProductResultQuery query) {
        return Message.success(reportService.corpusProductResult(query));
    }

    /**
     * 更新众测得分
     * @param updateSurveyScoreRequest
     * @return
     */
    @PostMapping("/updateSurveyScore")
    public Message<Integer> updateSurveyScore(@RequestBody UpdateSurveyScoreRequest updateSurveyScoreRequest) {
        return Message.success(reportService.updateSurveyScore(updateSurveyScoreRequest));
    }



    /**
     * 语料维度评测结果
     * @param query
     * @return
     */
    @Operation(summary = "语料维度评测结果查询")
    @PostMapping("/corpusReport")
    public Message<Page<CorpusReportInfo>> corpusReport(@RequestBody CorpusReportQuery query) {
        return Message.success(reportService.corpusReport(query));
    }


    /**
     * 压制比计算
     * @param query
     * @return
     */
    @Operation(summary = "压制比计算结果查询")
    @PostMapping("/contrastReport")
    public Message<List<ContrastReportInfo>> contrastReport(@Validated @RequestBody ContrastReportRequest query) {
        return Message.success(reportService.contrastReport(query));
    }


    /**
     * 压制比计算 二级场景
     * @param query
     * @return
     */
    @Operation(summary = "压制比计算结果查询二级场景")
    @PostMapping("/contrastReportSecondScene")
    public Message<List<ContrastReportInfo>> contrastReportSecondScene(@Validated @RequestBody ContrastReportRequest query) {
        Assert.hasLength(query.getScene(), "场景不能为空");
        return Message.success(reportService.contrastReportSecondScene(query));
    }


    /**
     * 压制比数据反查 自研大于竞品
     * @param query
     * @return
     */
    @Operation(summary = "自研大于竞品结果查询")
    @PostMapping("/selfProductWinCorpus")
    public Message<List<CorpusReportInfo>> querySelfProductWin(@Validated @RequestBody QueryProductWinRequest query) {
        return Message.success(reportService.querySelfProductWin(query));
    }


    /**
     * 压制比数据反查 竞品大于自研
     * @param query
     * @return
     */
    @Operation(summary = "竞品大于自研结果查询")
    @PostMapping("/competingProductWinCorpus")
    public Message<List<CorpusReportInfo>> queryCompletingProductWinCorpus(@Validated @RequestBody QueryProductWinRequest query) {
        return Message.success(reportService.queryCompletingProductWinCorpus(query));
    }



    @PostMapping("/queryReportPivot")
    public Message<List<Map<String, Object>>> queryReportPivot() {
        return Message.success(reportService.queryReportPivot());
    }

}

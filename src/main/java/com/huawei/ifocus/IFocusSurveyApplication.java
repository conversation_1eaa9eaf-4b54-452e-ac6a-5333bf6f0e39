/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.ifocus;

import org.apache.tomcat.util.http.Rfc6265CookieProcessor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * springboot 启动类
 *
 * @since 2022-01-10
 */
@SpringBootApplication(scanBasePackages = {"com.huawei.ifocus"})
@EnableScheduling
public class IFocusSurveyApplication {
    public static void main(String[] args) {
        SpringApplication.run(com.huawei.ifocus.IFocusSurveyApplication.class, args);
    }

    /**
     * Tomcat Cookie 处理配置 Bean
     *
     * @return WebServerFactoryCustomizer<TomcatServletWebServerFactory>
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> cookieProcessorCustomizer() {
        return factory -> factory.addContextCustomizers(
                context -> context.setCookieProcessor(new Rfc6265CookieProcessor()));
    }
}
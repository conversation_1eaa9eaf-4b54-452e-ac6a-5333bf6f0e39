package com.huawei.ifocus.service;

import com.huawei.ifocus.common.interceptors.UserHolder;
import com.huawei.ifocus.common.utils.DtoConvertUtil;
import com.huawei.ifocus.controller.request.*;
import com.huawei.ifocus.controller.response.*;
import com.huawei.ifocus.dto.*;
import com.huawei.ifocus.dto.ActivityPerson;
import com.huawei.ifocus.entity.Activity;
import com.huawei.ifocus.entity.ActivityFeedback;
import com.huawei.ifocus.entity.ActivityMaterial;
import com.huawei.ifocus.entity.CacheEntity;
import com.huawei.ifocus.repository.ActivityFeedBackRepository;
import com.huawei.ifocus.repository.ActivityMaterialRepository;
import com.huawei.ifocus.repository.ActivityRepository;
import com.huawei.ifocus.repository.CacheEntityRepository;

import com.fasterxml.jackson.databind.JsonNode;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * ActivityService
 *
 * @since 2025-04-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityService {
    private final ActivityRepository repository;
    private final ActivityMaterialRepository activityMaterialRepository;
    private final ActivityFeedBackRepository activityFeedBackRepository;
    private final UserHolder userHolder;
    private final MessageService messageService;
    private final ActivityRepository activityRepository;
    private final CacheEntityRepository cacheEntityRepository;

    private static final HashMap<String,String> activityTypeMap = new HashMap<>();
    static {
        activityTypeMap.put("文本评测", "text");
        activityTypeMap.put("图片评测", "picture");
        activityTypeMap.put("音频评测", "audio");
        activityTypeMap.put("视频评测", "video");
    }


    @Value("${message.jump-url}")
    private String jumpUrl;

    @Value("${message.template-no}")
    private String templateNo;



    /**
     * findAll
     *
     * @param query query
     * @return List<Activity>
     */
    public List<Activity> findAll(ActivityQuery query) {
        String fts = query.getFreeText();
        if (StringUtils.isBlank(fts)) {
            return StreamSupport.stream(repository.findAll().spliterator(), false)
                    .collect(Collectors.toList());
        }
        return repository.findAllByActivityNameContainingOrCreateUserContaining(fts, fts);
    }


    @Transactional(rollbackOn = Exception.class)
    public Integer createActivity(ActivityCreateReq activityCreateReq) {
        String w3 = userHolder.getW3().toLowerCase();
        String user = w3.toLowerCase(Locale.ROOT)+"-"+ userHolder.getCnName();
        Activity activity = DtoConvertUtil.copyFrom(activityCreateReq, Activity::new);
        activity.setCreateTime(LocalDateTime.now());
        activity.setUpdateTime(LocalDateTime.now());
        activity.setCreateUser(user);
        activity.setUpdateUser(user);
        repository.save(activity);
        saveMaterial(activityCreateReq.getMaterialGroupData(), activity);
        sendMessage(activity);
        return activity.getId();
    }


    private void saveMaterial(List<MaterialGroupData> materialGroupDataList, Activity activity) {
        List<ActivityMaterial> activityMaterialList =DtoConvertUtil.copyFrom(materialGroupDataList, ActivityMaterial::new);
        activityMaterialList.forEach(activityMaterial -> activityMaterial.setActivityId(activity.getId()));
        activityMaterialRepository.saveAll(activityMaterialList);
    }


    public void sendMessage(Activity activity){
        Map<String, String> templateTitleParams = new HashMap<>();
        templateTitleParams.put("activityName", activity.getActivityName());
        Map<String, String> templateContentParams = new HashMap<>();
        templateContentParams.put("creator", userHolder.getCnName());
        templateContentParams.put("createTime", activity.getCreateTime().toString());
        templateContentParams.put("activityName", activity.getActivityName());
        templateContentParams.put("endTime", activity.getEndTime());
        String jumpLink = this.jumpUrl + activity.getId().toString();
        String activityType = activityTypeMap.get(activity.getActivityType());
        jumpLink = jumpLink.replace("{activityType}", activityType);
        templateContentParams.put("link", jumpLink);
        templateContentParams.put("desc", activity.getActivityDesc());
        AppMessageDto appMessageDto = AppMessageDto.builder()
                .templateTitleParams(templateTitleParams)
                .templateContentParams(templateContentParams)
                .templateNo(this.templateNo)
                .toUserAccounts(activity.getPerson().stream().map(ActivityPerson::getW3).collect(Collectors.toList()))
                .jumpUrl(jumpLink)
                .build();
        ResponseEntity<String> responseEntity = messageService.sendTemplateMessage(appMessageDto);
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            return ;
        }
        log.error("send message failed,error info is: {}", responseEntity.getBody());
    }


    /**
     * deleteActivity
     *
     * @param id id
     * @return Boolean
     */
    @Transactional(rollbackOn = Exception.class)
    public Boolean deleteActivity(Integer id) {
        repository.deleteById(id);
        activityMaterialRepository.deleteAllByActivityId(id);
        activityFeedBackRepository.deleteAllByActivityId(id);
        return true;
    }


    /**
     * isDone
     *
     * @param activityId activityId
     * @return String
     */
    public Boolean isDone(Integer activityId) {
        String w3 = userHolder.getW3().toLowerCase()+"-"+userHolder.getCnName();
        List<ActivityFeedback> activityFeedbackList = activityFeedBackRepository.findAllByActivityIdAndProvider(activityId, w3);
        return !activityFeedbackList.isEmpty();
    }

    public ActivityViewPage queryAllActivity(ActivityQuery query) {
        String freeText = StringUtils.isBlank(query.getFreeText()) ? null : query.getFreeText();
        String activityType = StringUtils.isBlank(query.getActivityType()) ? null : query.getActivityType();
        String w3 = userHolder.getW3().toLowerCase(Locale.ROOT);
        if(query.getIsAdmin()){
            w3 = null;
        }
        List<Activity> activities = activityRepository.queryActivity(freeText, activityType, w3);
        ActivityViewPage res = new ActivityViewPage();

        List<ActivityView> collect = activities.stream().map(activity -> {
            ActivityView activityView = DtoConvertUtil.copyFrom(activity, ActivityView::new);
            activityView.setStatus(activityView.getActivityStatus());
            activityView.setIsDone(isDone(activityView.getId()));
            return activityView;
        }).filter(activityView -> {
            if (StringUtils.isNotEmpty(query.getStatus())) {
                return activityView.getStatus().equals(query.getStatus());
            } else {
                return true;
            }
        }).toList();
        res.setTotal(collect.size());
        res.setActivityViews(collect.stream().skip((long) (query.getCurrent() - 1) * query.getSize())
                            .limit(query.getSize())
                            .collect(Collectors.toList()));
        return  res;
    }


    /**
     * editActivity
     *
     * @param activityEditReq activityEditReq
     * @return Boolean
     */
    @Transactional(rollbackOn = Exception.class)
    public Boolean editActivity(ActivityEditReq activityEditReq) {
        Activity activityPre = repository.findById(activityEditReq.getId()).orElse(null);
        if (activityPre == null) {
            return false;
        }
        String user = userHolder.getW3().toLowerCase(Locale.ROOT)+"-"+ userHolder.getCnName();
        Activity activity = DtoConvertUtil.copyFrom(activityEditReq, Activity::new);
        activity.setActivityStatus(activityEditReq.getActivityStatus());
        activity.setDisplayWindowName(activityEditReq.getDisplayWindowName());
        activity.setUpdateTime(LocalDateTime.now());
        activity.setCreateTime(activityPre.getCreateTime());
        activity.setCreateUser(activityPre.getCreateUser());
        activity.setUpdateUser(user);
        repository.save(activity);
        if(!CollectionUtils.isEmpty(activityEditReq.getMaterialGroupData())){
            activityMaterialRepository.deleteAllByActivityId(activityEditReq.getId());
            saveMaterial(activityEditReq.getMaterialGroupData(), activity);
        }
        sendMessage(activity);
        return  true;
    }


    public ActivityInfoView getActivityInfo(Integer id) {
        Optional<Activity> activity = activityRepository.findById(id);
        if (activity.isPresent()) {
            ActivityInfoView activityInfoView = DtoConvertUtil.copyFrom(activity.get(), ActivityInfoView::new);
            List<ActivityMaterial> activityMaterialList = activityMaterialRepository.findAllByActivityId(id);
            List<MaterialGroupData> materialGroupData = DtoConvertUtil.copyFrom(activityMaterialList, MaterialGroupData::new);
            activityInfoView.setMaterialGroupData(materialGroupData);
            return activityInfoView;
        } else {
            return null;
        }
    }

    public Boolean saveScoreCache(ScoreCacheRequest scoreCacheRequest) {
        String key = scoreCacheRequest.getActivityId()+"-"+scoreCacheRequest.getW3();
        CacheEntity existingCacheEntity = cacheEntityRepository.queryCacheEntityByCacheKey(key);
        if (existingCacheEntity != null) {
            existingCacheEntity.setContent(scoreCacheRequest.getContent());
            cacheEntityRepository.save(existingCacheEntity);
        }else{
            cacheEntityRepository.save(new CacheEntity(key,scoreCacheRequest.getContent()));
        }
        return true;
    }

    public JsonNode queryScoreCache(ScoreCacheRequest scoreCacheRequest) {
        String key = scoreCacheRequest.getActivityId()+"-"+scoreCacheRequest.getW3();
        CacheEntity cacheEntity = cacheEntityRepository.queryCacheEntityByCacheKey(key);
        if(cacheEntity ==null){
            return null;
        }
        return cacheEntity.getContent();
    }

    @Transactional(rollbackOn = Exception.class)
    public Boolean deleteScoreCache(ScoreCacheRequest scoreCacheRequest) {
        String key = scoreCacheRequest.getActivityId()+"-"+scoreCacheRequest.getW3();
        cacheEntityRepository.deleteCacheEntityByCacheKey(key);
        return true;
    }
}

package com.huawei.ifocus.service;

import com.huawei.ifocus.common.exception.BizException;
import com.huawei.ifocus.common.utils.DtoConvertUtil;
import com.huawei.ifocus.common.utils.EasyExcelUtil;
import com.huawei.ifocus.common.utils.JacksonUtil;
import com.huawei.ifocus.controller.request.*;
import com.huawei.ifocus.controller.response.ActivityView;
import com.huawei.ifocus.controller.response.ReportStaticsView;
import com.huawei.ifocus.dto.*;
import com.huawei.ifocus.entity.Activity;
import com.huawei.ifocus.entity.ActivityMaterial;
import com.huawei.ifocus.entity.Corpus;
import com.huawei.ifocus.entity.Report;
import com.huawei.ifocus.repository.*;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;

import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Slf4j
@Service
@RequiredArgsConstructor

public class ReportService {

    private final ReportRepository reportRepository;
    private final ReportJdbcRepository reportJdbcRepository;
    private final JdbcTemplate jdbcTemplate;
    private final CorpusRepository corpusRepository;
    private final ActivityFeedBackRepository activityFeedBackRepository;
    private final ActivityMaterialRepository activityMaterialRepository;
    private final ActivityRepository activityRepository;
    private final AsyncService asyncService;

    /**
     * 删除报告
     *
     * @param ids 报告ID列表
     * @return 删除失败的报告ID列表
     */
    public List<Integer> delReport(List<Integer> ids) {
        List<Integer> failDeleted = new ArrayList<>();
        for (Integer id : ids) {
            Optional<Report> report = reportRepository.findById(id);
            List<ActivityMaterial> allByCorpusId = activityMaterialRepository.findAllByCorpusId(report.get().getCorpusId());
            if (!CollectionUtils.isEmpty(allByCorpusId)) {
                failDeleted.add(id);
                log.warn("Report id {} has  related activity materials, skipping deletion.", id);
            } else {
                reportRepository.deleteById(id);
            }
        }
        return failDeleted;
    }

    /**
     * 创建报告
     *
     * @param reportCreateReq 报告创建请求
     * @return 是否创建成功
     */
    public Boolean createReport(ReportCreateReq reportCreateReq) {
        Report report = DtoConvertUtil.copyFrom(reportCreateReq, Report::new);
        reportRepository.save(report);
        asyncService.processCorpusIdCountAsync(reportCreateReq.getCorpusId());
        return true;
    }

    @SneakyThrows
    public Page<ReportInfo> queryReportInfoPage(ReportQuery query) {
        Pageable pageable = PageRequest.of(query.getCurrent() - 1, query.getSize());
        String product = StringUtils.isBlank(query.getProduct()) ? null : query.getProduct();
        String version = StringUtils.isBlank(query.getVersion()) ? null : query.getVersion();
        String taskId = StringUtils.isBlank(query.getTaskId()) ? null : query.getTaskId();
        String taskSetName = StringUtils.isBlank(query.getTaskSetName()) ? null : query.getTaskSetName();
        String feature = StringUtils.isBlank(query.getFeature()) ? null : query.getFeature();
        String activity = StringUtils.isBlank(query.getActivity()) ? null : query.getActivity();
        String subFeature = StringUtils.isBlank(query.getSubFeature()) ? null : query.getSubFeature();
        String scene = StringUtils.isBlank(query.getScene()) ? null : query.getScene();
        String secondaryScene = StringUtils.isBlank(query.getSecondaryScene()) ? null : query.getSecondaryScene();
        String thirdScene = StringUtils.isBlank(query.getThirdScene()) ? null : query.getThirdScene();
        String userCorpus = StringUtils.isBlank(query.getUserCorpus()) ? null : query.getUserCorpus();
        String result = StringUtils.isBlank(query.getResult()) ? null : query.getResult();
        String type = StringUtils.isBlank(query.getType()) ? null : query.getType();
        LocalDateTime beginTime = query.getBeginTime();
        LocalDateTime endTime = query.getEndTime();
        Boolean hasSurveyScore = query.getHasSurveyScore();
        List<Integer> ids = CollectionUtils.isEmpty(query.getIds()) ? new ArrayList<>() : query.getIds();
        Integer corpusId = query.getCorpusId();
        Page<ReportInfoDto> reportInfoDtoPage = reportRepository.searchReportInfoNative(ids, product, version, corpusId, taskId, taskSetName, feature, subFeature, scene, secondaryScene
                , thirdScene, userCorpus, type, activity, result, hasSurveyScore, beginTime, endTime, pageable);
        return reportInfoDtoPage.map(reportInfoDto -> {
            ReportInfo reportInfo = DtoConvertUtil.copyFrom(reportInfoDto, ReportInfo::new);
            reportInfo.setResult(JacksonUtil.parseJsonToList(reportInfoDto.getResult()));
            reportInfo.setFunctionality(JacksonUtil.parseJsonToMap(reportInfoDto.getFunctionality()));
            reportInfo.setUserCorpus(JacksonUtil.parseJsonToList(reportInfoDto.getUserCorpus()));
            return reportInfo;
        });
    }

    /**
     * 导出报告
     *
     * @param query    查询条件
     * @param response HttpServletResponse
     * @throws IOException IO异常
     */
    public void export(ReportQuery query, HttpServletResponse response) throws IOException {
        query.setSize(100000);
        query.setCurrent(1);
        Page<ReportInfo> reportInfos = queryReportInfoPage(query);
        List<ReportInfoExcel> reportExcelDtoList = new ArrayList<>();
        reportInfos.get().forEach(reportInfo -> {
            ReportInfoExcel reportInfoExcel = DtoConvertUtil.copyFrom(reportInfo, ReportInfoExcel::new);
            reportInfoExcel.setUserCorpus(JacksonUtil.toJson(reportInfo.getUserCorpus()));
            reportInfoExcel.setResult(JacksonUtil.toJson(reportInfo.getResult()));
            reportInfoExcel.setFunctionality(JacksonUtil.toJson(reportInfo.getFunctionality()));
            reportExcelDtoList.add(reportInfoExcel);
        });
        EasyExcelUtil.exportToExcel(response, "测试报告", ReportInfoExcel.class, reportExcelDtoList);
    }

    /**
     * 查询报告统计信息
     *
     * @param query 查询条件
     * @return 报告统计视图
     */
    public ReportStaticsView queryReportStatistics(StatsQuery query) {
        ReportStaticsView view = new ReportStaticsView();
        Map<String, List<ProductScoreDto>> productScore = new HashMap<>();
        String activity = StringUtils.isEmpty(query.getActivity()) ? null : query.getActivity();
        List<String> sceneList = corpusRepository.queryAllSceneByFeature(query.getFeature(),activity);
        if (CollectionUtils.isEmpty(sceneList)) {
            return view;
        } else {
            for (String scene : sceneList) {
                List<ProductScoreDto> productScoreList = reportRepository.queryProductScoreByScene(scene, activity, query.getProductList());
                productScore.put(scene, productScoreList);
            }
        }
        view.setProductScore(productScore);

        return view;
    }

    /**
     * 查询众测活动详情
     *
     * @param taskId 任务ID
     * @return 活动视图列表
     */
    public List<ActivityView> queryReportSurveyDetail(String taskId) {
        List<Integer> ids = activityMaterialRepository.queryActivityIdListByTaskId(taskId);
        Iterable<Activity> iterable = activityRepository.findAllById(ids);
        List<Activity> activityList = StreamSupport.stream(iterable.spliterator(), false)
                .toList();
        return activityList.stream().map(activity -> {
            ActivityView activityView = DtoConvertUtil.copyFrom(activity, ActivityView::new);
            activityView.setStatus(activityView.getActivityStatus());
            return activityView;
        }).toList();
    }

    /**
     * 查询产品列表
     *
     * @param activity 活动名称
     * @param feature  特性名称
     * @return 产品列表
     */
    public List<String> queryProductList(String activity, String feature) {
        return reportRepository.queryProductList(activity, feature);
    }


    /**
     * 查询特性列表
     *
     * @param activity 活动名称
     * @return 特性列表
     */
    public List<String> queryFeatureList(String activity) {
        return reportRepository.queryFeatureList(activity);
    }

    /**
     * 查询语料聚合统计
     *
     * @param query 查询条件
     * @return 语料聚合统计信息
     */
    public Page<CorpusStatsInfo> queryCorpusStats(CorpusStatsQuery query) {
        Pageable pageable = PageRequest.of(query.getCurrent() - 1, query.getSize());
        List<String> activity = CollectionUtils.isEmpty(query.getActivity()) ? new ArrayList<>() : query.getActivity();
        Page<CorpusStatsDto> corpusStatsDtos = reportRepository.queryCorpusStatsByFeatureAndActivity(query.getFeature(), activity, pageable);
        return corpusStatsDtos.map(corpusStatsDto -> {
            CorpusStatsInfo corpusStatsInfo = DtoConvertUtil.copyFrom(corpusStatsDto, CorpusStatsInfo::new);
            corpusStatsInfo.setUserCorpus(JacksonUtil.parseJsonToList(corpusStatsDto.getUserCorpus()));
            corpusStatsInfo.setAttachment(JacksonUtil.parseJsonToList(corpusStatsDto.getAttachment()));

            List<ProductScoreDto> productScoreDtos = reportRepository.queryProductScoreByCorpusId(corpusStatsDto.getCorpusId(), activity);
            corpusStatsInfo.setProductScore(productScoreDtos);
            return corpusStatsInfo;
        });
    }


    /**
     * 删除报告
     *
     * @param req 删除请求
     * @return 是否删除成功
     */
    public Boolean deleteReport(ReportDeleteReq req) {
        reportRepository.deleteByCondition(req.getActivity(), req.getProduct(), req.getCorpusId(), req.getVersion());
        return true;
    }

    public List<String> corpusProductList(CorpusStatsQuery query) {
        List<String> activity = CollectionUtils.isEmpty(query.getActivity()) ? new ArrayList<>() : query.getActivity();
        List<String> feature = CollectionUtils.isEmpty(query.getFeature()) ? new ArrayList<>() : query.getFeature();
        return reportRepository.queryCorpusProductList(feature, activity);
    }

    public List<String> queryActivityList() {
        return reportRepository.queryActivityList();
    }

    public List<Map<String, Object>> queryCorpusScoreDetail(Integer activityId) {
        List<String> windowNames = activityFeedBackRepository.queryWindowNameByActivityId(activityId);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT f.activity_id,c.feature,c.sub_feature,c.scene,c.secondary_scene,c.third_scene,c.level, m.user_corpus, f.provider");
        for (String window : windowNames) {
            sql.append(", MAX(CASE WHEN f.window_name = '")
                    .append(window)
                    .append("' THEN f.score END) AS \"")
                    .append(window)
                    .append("\"");
            sql.append(", MAX(CASE WHEN f.window_name = '")
                    .append(window)
                    .append("' THEN  COALESCE(f.custom_feedback::text, '') || ' ' || COALESCE( f.score_reason::text, '')   END) AS \"")
                    .append(window)
                    .append("_反馈")
                    .append("\"");
        }
        sql.append(" FROM survey.activity_material m ")
                .append("JOIN survey.activity_feedback f ")
                .append("ON m.activity_id = f.activity_id AND m.group_index = f.group_index  join survey.corpus c on c.id  = m.corpus_id ")
                .append("WHERE m.activity_id = ")
                .append(activityId)
                .append(" GROUP BY f.activity_id,c.feature,c.sub_feature,c.scene,c.secondary_scene,c.third_scene,c.level, m.user_corpus, f.provider");
        log.info(sql.toString());
        return jdbcTemplate.queryForList(sql.toString());
    }

    /**
     * 导出语料打分详情
     *
     * @param response HttpServletResponse
     * @param id       活动ID
     * @throws IOException IO异常
     */
    public void corpusScoreDetailExport(HttpServletResponse response, Integer id) throws IOException {
        List<Map<String, Object>> result = queryCorpusScoreDetail(id);
        if (result.isEmpty()) {
            throw new BizException("没有数据");
        }

        List<String> head = new ArrayList<>(result.get(0).keySet());

        List<List<String>> data = new ArrayList<>();
        for (Map<String, Object> row : result) {
            List<String> line = new ArrayList<>();
            for (String key : head) {
                Object value = row.get(key);
                line.add(value != null ? value.toString() : "");
            }
            data.add(line);
        }

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("导出结果", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream())
                .head(head.stream().map(Collections::singletonList).toList()) //
                .sheet("sheet1")
                .doWrite(data);
    }

    /**
     * 查询语料对应产品打分结果
     *
     * @param query 查询条件
     * @return 语料产品打分结果列表
     */
    public List<CorpusProductResultInfo> corpusProductResult(CorpusProductResultQuery query) {
        return reportRepository.corpusProductResult(query.getCorpusId(), query.getActivity()).stream().map(item->{
            CorpusProductResultInfo info = DtoConvertUtil.copyFrom(item, CorpusProductResultInfo::new);
            info.setResult(JacksonUtil.parseJsonToJsonNode(item.getResult()));
            return info;
        }).toList();
    }

    /**
     * 导出语料聚合统计
     *
     * @param query    查询条件
     * @param response HttpServletResponse
     * @throws IOException IO异常
     */
    public void corpusStatsExport(CorpusStatsQuery query, HttpServletResponse response) throws IOException {
        query.setCurrent(1);
        query.setSize(100000);
        List<CorpusStatsInfo> corpusStatsInfos = queryCorpusStats(query).getContent();
        if (CollectionUtils.isEmpty(corpusStatsInfos)) {
            throw new BizException("没有数据");
        }
        List<CorpusStatsExcelDto> list = corpusStatsInfos.stream().map(corpusStatsInfo -> {
            CorpusStatsExcelDto corpusStatsExcelDto = DtoConvertUtil.copyFrom(corpusStatsInfo, CorpusStatsExcelDto::new);
            corpusStatsExcelDto.setUserCorpus(JacksonUtil.toJson(corpusStatsInfo.getUserCorpus()));
            corpusStatsExcelDto.setAttachment(JacksonUtil.toJson(corpusStatsInfo.getAttachment()));
            corpusStatsExcelDto.setProductScore(JacksonUtil.toJson(corpusStatsInfo.getProductScore()));
            return corpusStatsExcelDto;
        }).toList();
        EasyExcelUtil.exportToExcel(response, "语料聚合统计", CorpusStatsExcelDto.class, list);
    }

    /**
     * 更新众测打分
     *
     * @param request 更新请求
     * @return 更新的行数
     */
    public Integer updateSurveyScore(UpdateSurveyScoreRequest request) {
        return reportRepository.updateSurveyScore(request.getId(), request.getScore());
    }


    /**
     * 查询语料对应产品打分结果
     *
     * @param corpusIdList 语料ID列表
     * @param productList  产品列表
     * @return 语料产品打分结果列表
     */
    public List<CorpusReportInfo> queryCorpusProductResult(List<ActivityCorpusDto> corpusIdList,List<String> productList) {
        return corpusIdList.stream().map(activityCorpusDto -> {
            Corpus corpus = corpusRepository.findById(activityCorpusDto.getCorpusId()).get();
            CorpusReportInfo corpusReportInfo = DtoConvertUtil.copyFrom(corpus, CorpusReportInfo::new);
            corpusReportInfo.setActivity(activityCorpusDto.getActivity());
            corpusReportInfo.setCorpusId(corpus.getId());
            List<Report> reportList = reportRepository.queryByActivityAndCorpusIdAndProduct(activityCorpusDto.getActivity(), activityCorpusDto.getCorpusId(), productList);
            List<ProductResultScoreDto> productResultScoreDtos = DtoConvertUtil.copyFrom(reportList, ProductResultScoreDto::new);
            Map<String, List<ProductResultScoreDto>> collect = productResultScoreDtos.stream().collect(Collectors.groupingBy(ProductResultScoreDto::getProduct));
            Map<String, ProductResultScoreDto> res = new HashMap<>();
            collect.entrySet().stream()
                    .filter(entry -> !entry.getValue().isEmpty())
                    .forEach(entry->{
                        res.put(entry.getKey(), entry.getValue().get(0));
                    });
            corpusReportInfo.setProductScore(res);
            return corpusReportInfo;
        }).toList();
    }


    /**
     * 语料维度评测结果查询
     *
     * @param query 查询条件
     * @return 分页的语料报告信息
     */
    public Page<CorpusReportInfo> corpusReport(CorpusReportQuery query) {
        Pageable pageable = PageRequest.of(query.getCurrent() - 1, query.getSize());
        String result = StringUtils.isBlank(query.getResult()) ? null : query.getResult();
        String feature = StringUtils.isBlank(query.getFeature()) ? null : query.getFeature();
        String subFeature = StringUtils.isBlank(query.getSubFeature()) ? null : query.getSubFeature();
        String product = StringUtils.isBlank(query.getProduct()) ? null : query.getProduct();
        String scene = StringUtils.isBlank(query.getScene()) ? null : query.getScene();
        String secondaryScene = StringUtils.isBlank(query.getSecondaryScene()) ? null : query.getSecondaryScene();
        String activity = StringUtils.isBlank(query.getActivity()) ? null : query.getActivity();

        Page<ActivityCorpusDto> corpusIdList = reportRepository.queryAllCorpusId(feature,subFeature,product,activity,scene,secondaryScene,result,pageable);

        return corpusIdList.map(activityCorpusDto -> {
            Corpus corpus = corpusRepository.findById(activityCorpusDto.getCorpusId()).get();
            CorpusReportInfo corpusReportInfo = DtoConvertUtil.copyFrom(corpus, CorpusReportInfo::new);
            corpusReportInfo.setActivity(activityCorpusDto.getActivity());
            corpusReportInfo.setCorpusId(corpus.getId());
            List<Report> reportList = reportRepository.queryByActivityAndCorpusIdAndProduct(activityCorpusDto.getActivity(), activityCorpusDto.getCorpusId(), query.getProductList());
            List<ProductResultScoreDto> productResultScoreDtos = DtoConvertUtil.copyFrom(reportList, ProductResultScoreDto::new);
            Map<String, List<ProductResultScoreDto>> collect = productResultScoreDtos.stream().collect(Collectors.groupingBy(ProductResultScoreDto::getProduct));
            Map<String, ProductResultScoreDto> res = new HashMap<>();
            collect.entrySet().stream()
                    .filter(entry -> !entry.getValue().isEmpty())
                    .forEach(entry->{
                        res.put(entry.getKey(), entry.getValue().get(0));
                    });
            corpusReportInfo.setProductScore(res);
            return corpusReportInfo;
        });
    }


    /**
     * 压制比计算（支持场景和二级场景）
     *
     * @param query 查询条件
     * @param useSecondScene 是否按二级场景
     * @return 压制比报告信息列表
     */
    public List<ContrastReportInfo> contrastReportGeneral(ContrastReportRequest query, boolean useSecondScene) {
        List<String> sceneList;
        if (useSecondScene) {
            sceneList = reportJdbcRepository.querySecondarySceneList(query.getScene(), query.getActivity(), query.getSelfProduct(),query.getFeature());
        } else {
            sceneList = reportJdbcRepository.querySceneList(query.getActivity(), query.getSelfProduct(), query.getFeature());
        }
        List<ContrastReportInfo> res = new ArrayList<>();
        for (String scene : sceneList) {
            ContrastReportInfo info = new ContrastReportInfo();
            info.setScene(scene);
            List<ProductIntegerDto> selfProductWinList = new ArrayList<>();
            for (String completeProduct : query.getCompetingProductList()) {
                Integer count = useSecondScene
                        ? reportJdbcRepository.countSelfProductWinSecondScene(scene, query.getActivity(), query.getSelfProduct(), completeProduct,query.getFeature())
                        : reportJdbcRepository.countSelfProductWinScene(scene, query.getActivity(), query.getSelfProduct(), completeProduct, query.getFeature());
                selfProductWinList.add(new ProductIntegerDto(count, completeProduct));
            }
            info.setSelfProductWinList(selfProductWinList);

            List<ProductIntegerDto> completeProductWinList = new ArrayList<>();
            for (String completeProduct : query.getCompetingProductList()) {
                Integer count = useSecondScene
                        ? reportJdbcRepository.countCompetingProductWinBySecondScene(scene, query.getActivity(), query.getSelfProduct(), completeProduct,query.getFeature())
                        : reportJdbcRepository.countCompetingProductWinByScene(scene, query.getActivity(), query.getSelfProduct(), completeProduct, query.getFeature());
                completeProductWinList.add(new ProductIntegerDto(count, completeProduct));
            }
            info.setCompetingProductWinList(completeProductWinList);

            List<ProductDoubleDto> avgValueList = new ArrayList<>();
            List<String> allProducts = new ArrayList<>(query.getCompetingProductList());
            allProducts.add(query.getSelfProduct());
            for (String completeProduct : allProducts) {
                Double count = useSecondScene
                        ? reportJdbcRepository.calculateAvgBySecondScene(scene, query.getActivity(), completeProduct,query.getFeature())
                        : reportJdbcRepository.calculateAvgByScene(scene, query.getActivity(), completeProduct, query.getFeature());
                if (count == null) {
                    count = 0.0;
                }
                avgValueList.add(new ProductDoubleDto(count, completeProduct));
            }
            info.setAvgValueList(avgValueList);

            setContrastList(selfProductWinList, completeProductWinList, info);
            res.add(info);
        }
        return res;
    }

    private static void setContrastList(List<ProductIntegerDto> selfProductWinList, List<ProductIntegerDto> completeProductWinList, ContrastReportInfo info) {
        List<ProductDoubleDto> contrastValueList = new ArrayList<>();
        for (int i = 0; i < selfProductWinList.size(); i++) {
            ProductIntegerDto selfProductWin = selfProductWinList.get(i);
            ProductIntegerDto completeProductWin = completeProductWinList.get(i);
            if (completeProductWin.getNum() == 0) {
                contrastValueList.add(new ProductDoubleDto(0.0, completeProductWin.getProduct()));
            } else {
                double contrastValue = (double) selfProductWin.getNum() / completeProductWin.getNum();
                contrastValueList.add(new ProductDoubleDto(contrastValue, completeProductWin.getProduct()));
            }
        }
        info.setContrastValueList(contrastValueList);
    }

    /**
     * 压制比计算（场景）
     */
    public List<ContrastReportInfo> contrastReport(ContrastReportRequest query) {
        return contrastReportGeneral(query, false);
    }

    /**
     * 压制比计算（二级场景）
     */
    public List<ContrastReportInfo> contrastReportSecondScene(ContrastReportRequest query) {
        return contrastReportGeneral(query, true);
    }


    public List<Map<String, Object>> queryReportPivot() {
        return jdbcTemplate.queryForList("SELECT * FROM survey.v_report_pivot");
    }

    /**
     * 查询自研大于竞品的语料
     *
     * @param query 查询条件
     * @return 自研大于竞品的语料报告信息列表
     */
    public List<CorpusReportInfo> querySelfProductWin(QueryProductWinRequest query) {
        List<Integer> corpusIds;
        if(StringUtils.isBlank(query.getSecondaryScene())){
            corpusIds = reportJdbcRepository.queryCorpusIdBySelfProductWin(query.getActivity(), query.getScene(),query.getSelfProduct(), query.getCompetingProduct(),query.getFeature());
        }else{
            corpusIds = reportJdbcRepository.queryCorpusIdBySelfProductWinSecondScene(query.getActivity(),query.getScene(), query.getSecondaryScene(),query.getSelfProduct(), query.getCompetingProduct(),query.getFeature());
        }
        return  queryCorpusProductResult(corpusIds.stream().map(
                corpusId -> new ActivityCorpusDto(query.getActivity(),corpusId)).toList(),
                Lists.newArrayList(query.getSelfProduct(), query.getCompetingProduct()));
    }

    /**
     * 查询竞品大于自研的语料
     *
     * @param query 查询条件
     * @return 竞品大于自研的语料报告信息列表
     */
    public List<CorpusReportInfo> queryCompletingProductWinCorpus(QueryProductWinRequest query) {
        List<Integer> corpusIds;
        if(StringUtils.isBlank(query.getSecondaryScene())) {
            corpusIds = reportJdbcRepository.queryCorpusIdByCompetingProductWin(query.getActivity(), query.getScene(), query.getSelfProduct(), query.getCompetingProduct(),query.getFeature());
        }else {
            corpusIds = reportJdbcRepository.queryCorpusIdByCompetingProductWinSecondScene(query.getActivity(), query.getScene(),query.getSecondaryScene(), query.getSelfProduct(), query.getCompetingProduct(),query.getFeature());
        }
        return  queryCorpusProductResult(corpusIds.stream().map(
                corpusId -> new ActivityCorpusDto(query.getActivity(),corpusId)).toList(),
                Lists.newArrayList(query.getSelfProduct(), query.getCompetingProduct()));
    }
}


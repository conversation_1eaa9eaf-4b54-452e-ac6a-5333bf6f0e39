package com.huawei.ifocus.interceptors;

import com.huawei.ifocus.common.exception.BizException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class ExternalApiAuthInter<PERSON> implements HandlerInterceptor {

    private static final String AUTH_HEADER = "auth";
    private static final String VALID_AUTH_VALUE = "*************************************";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String authValue = request.getHeader(AUTH_HEADER);
        if (VALID_AUTH_VALUE.equals(authValue)) {
            return true;
        }
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        throw new BizException( "未授权请求");
    }
}
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */

package com.huawei.ifocus.interceptors;

import com.huawei.ifocus.common.constants.Constants;
import com.huawei.ifocus.common.interceptors.UserHolder;
import com.huawei.ifocus.common.utils.Message;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.io.PrintWriter;

/**
 * 登录拦截器
 *
 * @since 2023-07-19
 */
@Slf4j
@Component
public class UserLoginInterceptor implements HandlerInterceptor {
    @Autowired
    private UserHolder cookieHolder;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        log.info("preHandle, reqUrl : {}", request.getRequestURL().toString());
        String reqUri = request.getRequestURI().replace(request.getContextPath(), "");
        // 使用cookie验证登录
        if (cookieHolder.checkLogin()) {
            // 记录访问信息
            log.info("{} request : {}", cookieHolder.getW3(), reqUri);
            return true;
        } else {
            response.setCharacterEncoding(Constants.CHARSET_UTF_8);
            response.setContentType("application/json; charset=utf-8");

            PrintWriter out = null;
            try {
                out = response.getWriter();
                out.append(Message.loginTimedOut().toJSONStr());
            } finally {
                if (out != null) {
                    out.close();
                }
            }

            log.warn("XMLHttpRequest login timeout");
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse arg1, Object arg2, ModelAndView modelAndView)
            throws Exception {
        log.debug("postHandle");
    }

    @Override
    public void afterCompletion(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2, Exception arg3)
            throws Exception {
        log.debug("afterCompletion");
    }

}

package com.huawei.ifocus.repository;

import com.huawei.ifocus.dto.*;
import com.huawei.ifocus.entity.ActivityFeedback;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActivityFeedBackRepository extends
        PagingAndSortingRepository<ActivityFeedback, Integer>,
        CrudRepository<ActivityFeedback, Integer>,
        JpaSpecificationExecutor<ActivityFeedback> {


    void deleteAllByActivityId(Integer id);


    @Query(value = """
          SELECT DISTINCT ON (provider) * FROM survey.activity_feedback
          WHERE activity_id = :id
        """, nativeQuery = true)
    List<ActivityFeedback> findAllByActivityId(Integer id);

    @Query(value = """
          SELECT DISTINCT ON (provider) provider FROM survey.activity_feedback
          WHERE activity_id = :id
        """, nativeQuery = true)
    List<String> findDoneProviderByActivityId(Integer id);

    @Query(value = """
          SELECT DISTINCT ON (window_name) window_name FROM survey.activity_feedback
          WHERE activity_id = :id
        """, nativeQuery = true)
    List<String> queryWindowNameByActivityId(Integer id);



    @Query(value = """
          select window_name as product ,avg(score) as score
         from survey.activity_feedback\s
         where activity_id = :activityId
         and corpus_id =:corpusId
         group by window_name
        """, nativeQuery = true)
    List<ProductScoreDto> queryWindowScoreByActivityIdAndCorpusId(Integer activityId,Integer corpusId);

    List<ActivityFeedback> findAllByActivityIdAndProvider(Integer activityId, String provider);

    @Query(value = """
          SELECT distinct task_id
                FROM survey.activity_feedback
        """, nativeQuery = true)
    List<String> queryAllTaskId();

    @Query(value = """
              SELECT AVG(score)
                    FROM survey.activity_feedback
                    WHERE task_id = :taskId; 
            """, nativeQuery = true)
    Double querySurveyScoreByTaskId(String taskId);


    @Query(value = """
              SELECT s.score,\s
                     COALESCE(COUNT(af.score)::float / NULLIF((SELECT COUNT(*) FROM survey.activity_feedback WHERE activity_id = :activityId), 0), 0) AS proportion
              FROM (SELECT generate_series(1, 5) AS score) s
              LEFT JOIN survey.activity_feedback af\s
              ON s.score = af.score AND af.activity_id = :activityId
              GROUP BY s.score
              ORDER BY s.score;
            """, nativeQuery = true)
    List<ScoreRateDto> queryScoreRate(Integer activityId);



    @Query(value = """
         SELECT window_name as windowName,\s
                COUNT(*)::float / NULLIF((SELECT COUNT(*) FROM survey.activity_feedback WHERE activity_id = :activityId), 0) AS proportion
         FROM survey.activity_feedback\s
         WHERE activity_id = :activityId AND score = :score
         GROUP BY window_name;
        """, nativeQuery = true)
    List<WindowsRateDto> queryWindowRateByScore(Integer activityId, Integer score);


    @Query(value = """
            SELECT window_name as windowName, COUNT(*)::float / SUM(COUNT(*)) OVER () as proportion  
            FROM survey.activity_feedback 
            WHERE activity_id = :activityId AND score = :score AND score_reason @> to_jsonb(ARRAY[:reason]) 
            GROUP BY window_name
            """, nativeQuery = true)
    List<WindowsRateDto> queryProportionByReason(@Param("reason") String reason,
                                                 @Param("activityId") Integer activityId,
                                                 @Param("score") Integer score);


    @Query(value = """
            SELECT 
                window_name as windowName,
                task_id as taskId,
                group_index as groupIndex,
                score as score,
                score_reason as scoreReason,
                custom_feedback as customFeedback
            FROM survey.activity_feedback 
            WHERE activity_id = :id AND provider ILIKE  :user || '%' 
            """, nativeQuery = true)
    List<FeedbackDataDto> queryAllByUserAndActivityId(Integer id, String user);


    List<ActivityFeedback> queryByActivityId(Integer activityId);

    @Query(value = """
        SELECT distinct task_id
        FROM survey.activity_feedback 
        where activity_id=:activityId
        """, nativeQuery = true)
    List<String> queryTaskIdListByActivityId(Integer activityId);


    @Query(value = """
        SELECT distinct corpus_id
        FROM survey.activity_feedback 
        where activity_id=:activityId
        """, nativeQuery = true)
    List<Integer> queryAllCorpusIdByActivityId(Integer activityId);

    @Query(value = """
        select provider  ,score
         from survey.activity_feedback
         where activity_id = :activityId
         and corpus_id =:corpusId
         and window_name =:product
        """, nativeQuery = true)
    List<ProviderScoreDto> queryAllUserCorpusScore(Integer activityId, Integer corpusId, String product);




}

package com.huawei.ifocus.repository;

import com.huawei.ifocus.entity.Activity;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActivityRepository extends
        PagingAndSortingRepository<Activity, Integer>,
        CrudRepository<Activity, Integer>,
        JpaSpecificationExecutor<Activity> {


    @Query(value = """
    select a.score_reason_config->:score  from survey.activity a  where id=:activityId
    """,nativeQuery = true)
     String queryScoreReasonList(Integer activityId, String score);

    List<Activity> findAllByActivityNameContainingOrCreateUserContaining(String activityName, String creator);


    @Query(value = """
    SELECT *
    FROM survey.activity
    WHERE EXISTS (
        SELECT 1
        FROM jsonb_array_elements(person) AS elem
        WHERE elem->>'w3' = :w3
    )
    """, nativeQuery = true)
    List<Activity> findByW3(@Param("w3") String w3);


    @Query(value = """
    SELECT *
    FROM survey.activity
    WHERE 
        activity_type = :activityType
        AND (:freeText IS NULL OR activity_name ILIKE '%' || :freeText || '%')
        AND (:w3 is NULL OR EXISTS (
            SELECT 1
            FROM jsonb_array_elements(person) AS elem
            WHERE elem->>'w3' = :w3)
        )
    order by id desc
    """, nativeQuery = true)
    List<Activity> queryActivity(String freeText, String activityType, String w3);


    @Query(
            value = """
        select corpus_id from survey.activity
        where id = :id
    """,nativeQuery = true)
    List<Integer> queryCorpusIdListById(Integer id);

}

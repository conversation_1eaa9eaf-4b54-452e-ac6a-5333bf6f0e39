package com.huawei.ifocus.repository;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class ReportJdbcRepository {

    @Autowired
    private JdbcTemplate jdbcTemplate;


    public List<String> querySceneList(String activity, String selfProduct, String feature) {
        String sql = String.format("""
            SELECT distinct scene
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            where v.activity = '%s'
            and v."%s" is not null
            and c.feature = '%s'
        """, activity, selfProduct, feature);
        log.debug(sql);
        return jdbcTemplate.queryForList(sql, String.class);
    }

    public List<String> querySecondarySceneList(String scene, String activity, String selfProduct, String feature) {
        String sql = String.format("""
            SELECT distinct secondary_scene
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            where v.activity = '%s'
            and c.scene = '%s'
            and v."%s" is not null
            and c.feature = '%s'
        """, activity, scene, selfProduct, feature);
        log.debug(sql);
        return jdbcTemplate.queryForList(sql, String.class);
    }



    public Double calculateAvgByScene(String scene, String activity, String product, String feature) {
        String sql = String.format("""
            SELECT avg(v."%s")
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            where v.activity = '%s'
            and c.scene = '%s'
            and v."%s" is not null
            and c.feature = '%s'
        """, product, activity, scene, product, feature);
        log.debug(sql);
        return jdbcTemplate.queryForObject(sql, Double.class);
    }

    public Double calculateAvgBySecondScene(String scene, String activity, String product, String feature) {
        String sql = String.format("""
            SELECT avg(v."%s")
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            where v.activity = '%s'
            and c.secondary_scene = '%s'
            and v."%s" is not null
            and c.feature = '%s'
        """, product, activity, scene, product, feature);
        log.debug(sql);
        return jdbcTemplate.queryForObject(sql, Double.class);
    }


    public Integer countSelfProductWinScene(String scene, String activity, String selfProduct, String competingProduct, String feature) {
        String sql = String.format("""
            SELECT COUNT(*)
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            WHERE
            v.activity = ?
            AND v."%s" IS NOT NULL
            AND v."%s" IS NOT NULL
            AND c.scene = ?
            AND v."%s"  >= v."%s"
            AND c.feature = ?
        """, selfProduct, competingProduct, selfProduct, competingProduct);
        log.debug(sql);
        return jdbcTemplate.queryForObject(sql, new Object[]{activity, scene, feature}, Integer.class);
    }

    public Integer countSelfProductWinSecondScene(String scene, String activity, String selfProduct, String competingProduct, String feature) {
        String sql = String.format("""
            SELECT COUNT(*)
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            WHERE
            v.activity = ?
            AND v."%s" IS NOT NULL
            AND v."%s" IS NOT NULL
            AND c.secondary_scene = ?
            AND v."%s"  >= v."%s"
            AND c.feature = ?
        """, selfProduct, competingProduct, selfProduct, competingProduct);
        log.debug(sql);
        return jdbcTemplate.queryForObject(sql, new Object[]{activity, scene, feature}, Integer.class);
    }

    public Integer countCompetingProductWinByScene(String scene, String activity, String selfProduct, String competingProduct, String feature) {
        String sql = String.format("""
            SELECT COUNT(*)
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            WHERE
            v.activity = ?
            AND v."%s" IS NOT NULL
            AND v."%s" IS NOT NULL
            AND c.scene = ?
            AND v."%s"  <= v."%s"
            AND c.feature = ?
        """, selfProduct, competingProduct, selfProduct, competingProduct);
        log.debug(sql);
        return jdbcTemplate.queryForObject(sql, new Object[]{activity, scene, feature}, Integer.class);
    }

    public Integer countCompetingProductWinBySecondScene(String scene, String activity, String selfProduct, String competingProduct, String feature) {
        String sql = String.format("""
            SELECT COUNT(*)
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            WHERE
            v.activity = ?
            AND v."%s" IS NOT NULL
            AND v."%s" IS NOT NULL
            AND c.secondary_scene = ?
            AND v."%s"  <= v."%s"
            AND c.feature = ?
        """, selfProduct, competingProduct, selfProduct, competingProduct);
        log.debug(sql);
        return jdbcTemplate.queryForObject(sql, new Object[]{activity, scene, feature}, Integer.class);
    }


    public List<Integer> queryCorpusIdBySelfProductWin(String activity, String scene, String selfProduct, String competingProduct, String feature) {
        String sql = String.format("""
            SELECT corpus_id
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            WHERE
            v.activity = '%s'
            AND v."%s" IS NOT NULL
            AND v."%s" IS NOT NULL
            AND c.scene = '%s'
            AND v."%s"  >= v."%s"
            AND c.feature = '%s'
        """, activity, selfProduct, competingProduct, scene, selfProduct, competingProduct, feature);
        log.debug(sql);
        return jdbcTemplate.queryForList(sql, Integer.class);
    }


    public List<Integer> queryCorpusIdByCompetingProductWin(String activity, String scene, String selfProduct, String competingProduct, String feature) {
        String sql = String.format("""
            SELECT corpus_id
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            WHERE
            v.activity = '%s'
            AND v."%s" IS NOT NULL
            AND v."%s" IS NOT NULL
            AND c.scene = '%s'
            AND v."%s"  <= v."%s"
            AND c.feature = '%s'
        """, activity, selfProduct, competingProduct, scene, selfProduct, competingProduct, feature);
        log.debug(sql);
        return jdbcTemplate.queryForList(sql, Integer.class);
    }


    public List<Integer> queryCorpusIdBySelfProductWinSecondScene(String activity, String scene, String secondaryScene, String selfProduct, String completingProduct, String feature) {
        String sql = String.format("""
            SELECT corpus_id
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            WHERE
            v.activity = '%s'
            AND v."%s" IS NOT NULL
            AND v."%s" IS NOT NULL
            AND c.scene = '%s'
            AND c.secondary_scene = '%s'
            AND v."%s"  >= v."%s"
            AND c.feature = '%s'
        """, activity, selfProduct, completingProduct, scene, secondaryScene, selfProduct, completingProduct, feature);
        log.debug(sql);
        return jdbcTemplate.queryForList(sql, Integer.class);
    }

    public List<Integer> queryCorpusIdByCompetingProductWinSecondScene(String activity, String scene, String secondaryScene, String selfProduct, String completingProduct, String feature) {
        String sql = String.format("""
            SELECT corpus_id
            FROM survey.v_report_pivot v
            LEFT JOIN survey.corpus c ON v.corpus_id = c.id
            WHERE
            v.activity = '%s'
            AND v."%s" IS NOT NULL
            AND v."%s" IS NOT NULL
            AND c.scene = '%s'
            AND c.secondary_scene = '%s'
            AND v."%s"  <= v."%s"
            AND c.feature = '%s'
        """, activity, selfProduct, completingProduct, scene, secondaryScene, selfProduct, completingProduct, feature);
        log.debug(sql);
        return jdbcTemplate.queryForList(sql, Integer.class);
    }
}

package com.huawei.ifocus.repository;

import com.huawei.ifocus.entity.CacheEntity;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;


@Repository
public interface CacheEntityRepository extends JpaRepository<CacheEntity, Integer>,JpaSpecificationExecutor<CacheEntity> {

    CacheEntity queryCacheEntityByCacheKey(String key);

    void deleteCacheEntityByCacheKey(String key);


}

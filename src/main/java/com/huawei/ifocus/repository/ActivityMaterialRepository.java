package com.huawei.ifocus.repository;

import com.huawei.ifocus.entity.ActivityMaterial;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ActivityMaterialRepository extends JpaRepository<ActivityMaterial, Integer>,JpaSpecificationExecutor<ActivityMaterial> {


    List<ActivityMaterial> findAllByActivityId(Integer id);


    List<ActivityMaterial> findAllByCorpusId(Integer corpusId);

    void deleteAllByActivityId(Integer id);


    @Query(value = """
        SELECT DISTINCT jsonb_array_elements(window_data)->>'windowName' AS window_name
        FROM survey.activity_material where activity_id = :activityId;
    """,nativeQuery = true)
    List<String> queryActivityWindowsName(Integer activityId);


    @Query(value = """
        SELECT DISTINCT activity_id
        FROM survey.activity_material
        WHERE EXISTS (
            SELECT 1
            FROM jsonb_array_elements(window_data) AS elem
            WHERE elem->>'taskId' = :taskId
        );;
    """,nativeQuery = true)
    List<Integer> queryActivityIdListByTaskId(String taskId);

    ActivityMaterial queryByActivityIdAndGroupIndex(Integer activityId, Integer groupIndex);
    
}

package com.huawei.ifocus.repository;

import com.huawei.ifocus.dto.*;
import com.huawei.ifocus.entity.Report;

import jakarta.transaction.Transactional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ReportRepository extends
        PagingAndSortingRepository<Report, Integer>,
        JpaRepository<Report, Integer>,
        JpaSpecificationExecutor<Report> {


    @Modifying
    @Transactional
    @Query(value = "UPDATE survey.report SET survey_score = :newScore WHERE task_id = :taskId and is_revise = 0", nativeQuery = true)
    int updateScoreByTaskId(@Param("newScore") Float newScore, @Param("taskId") String taskId);

    @Query(
            value = """
            SELECT 
                id,
                corpusId,
                taskId,
                product,
                version,
                startTime,
                endTime,
                performance,
                reliability,
                result,
                functionality,
                surveyScore,
                feature,
                subFeature,
                scene,
                secondaryScene,
                thirdScene,
                level,
                userCorpus,
                type,
                taskSetName,
                activity,
                reportLink 
        FROM (        
            SELECT 
                r.id,
                r.corpus_id AS corpusId,
                r.task_id AS taskId,
                r.product,
                r.version,
                r.start_time AS startTime,
                r.end_time AS endTime,
                r.performance,
                r.reliability,
                r.result,
                r.functionality,
                r.survey_score AS surveyScore,
                c.feature AS feature ,
                c.sub_feature AS subFeature ,
                c.scene AS scene ,
                c.secondary_scene AS secondaryScene ,
                c.third_scene AS thirdScene ,
                c.level AS level ,
                c.user_corpus AS userCorpus ,
                c.type AS type ,
                r.task_set_name AS taskSetName ,
                r.activity AS activity ,
                r.report_link AS reportLink ,
                ROW_NUMBER() OVER (PARTITION BY r.corpus_id ORDER BY r.end_time DESC) AS rn,
                           MAX(r.end_time) OVER (PARTITION BY r.corpus_id) AS max_end_time
            FROM survey.report r
            LEFT JOIN survey.corpus c ON r.corpus_id = c.id
            WHERE 
                (:product IS NULL OR r.product = :product)
                AND (:ids IS NULL OR r.id in :ids)
                AND (:version IS NULL OR r.version = :version)
                AND (:corpusId IS NULL OR r.corpus_id = :corpusId)
                AND (:taskId IS NULL OR r.task_id = :taskId)
                AND (:type IS NULL OR c.type = :type)
                AND (:secondaryScene IS NULL OR c.secondary_scene ILIKE '%' || :secondaryScene || '%')
                AND (:activity IS NULL OR r.activity ILIKE '%' || :activity || '%')
                AND (:thirdScene IS NULL OR c.third_scene  ILIKE '%' || :thirdScene || '%' )
                AND (:scene IS NULL OR c.scene ILIKE '%' || :scene || '%' )
                AND (:subFeature IS NULL OR c.sub_feature ILIKE '%' || :subFeature || '%')
                AND (:taskSetName IS NULL OR r.task_set_name  ILIKE '%' || :taskSetName || '%')
                AND (:feature IS NULL OR c.feature ILIKE '%' || :feature || '%' )
                AND (
                      :hasSurveyScore IS NULL
                      OR (:hasSurveyScore = TRUE AND r.survey_score IS NOT NULL)
                      OR (:hasSurveyScore = FALSE AND r.survey_score IS NULL)
                      )
                AND (
                        :userCorpus IS NULL OR EXISTS (
                            SELECT 1 FROM jsonb_array_elements_text(c.user_corpus) AS elem
                            WHERE elem ILIKE '%' || :userCorpus || '%'
                        )
                    )
              AND (
                        :result IS NULL OR EXISTS (
                            SELECT 1 FROM jsonb_array_elements_text(r.result) AS elem
                            WHERE elem ILIKE '%' || :result || '%'
                        )
                    )
                AND (CAST(:beginTime AS timestamp) IS NULL OR ( r.start_time BETWEEN CAST(:beginTime AS timestamp) AND CAST(:endTime AS timestamp)))
            ) t
            ORDER BY max_end_time DESC, corpusId, rn
            """,

            countQuery = """
            SELECT COUNT(1)  
            FROM survey.report r
            LEFT JOIN survey.corpus c ON r.corpus_id = c.id
            WHERE 
                (:product IS NULL OR r.product = :product)
                AND (:version IS NULL OR r.version = :version)
                AND (:ids  IS NULL OR r.id in :ids)
                AND (:corpusId IS NULL OR r.corpus_id = :corpusId)
                AND (:taskId IS NULL OR r.task_id = :taskId)
                AND (:type IS NULL OR c.type = :type)
                AND (:secondaryScene IS NULL OR c.secondary_scene ILIKE '%' || :secondaryScene || '%')
                AND (:activity IS NULL OR r.activity ILIKE '%' || :activity || '%')
                AND (:thirdScene IS NULL OR c.third_scene  ILIKE '%' || :thirdScene || '%' )
                AND (:scene IS NULL OR c.scene ILIKE '%' || :scene || '%' )
                AND (:subFeature IS NULL OR c.sub_feature ILIKE '%' || :subFeature || '%')
                AND (:taskSetName IS NULL OR r.task_set_name  ILIKE '%' || :taskSetName || '%')
                AND (:feature IS NULL OR c.feature ILIKE '%' || :feature || '%' )
                AND (
                      :hasSurveyScore IS NULL
                      OR (:hasSurveyScore = TRUE AND r.survey_score IS NOT NULL)
                      OR (:hasSurveyScore = FALSE AND r.survey_score IS NULL)
                      )
                AND (
                        :userCorpus IS NULL OR EXISTS (
                            SELECT 1 FROM jsonb_array_elements_text(c.user_corpus) AS elem
                            WHERE elem ILIKE '%' || :userCorpus || '%'
                        )
                    )
               AND (
                        :result IS NULL OR EXISTS (
                            SELECT 1 FROM jsonb_array_elements_text(r.result) AS elem
                            WHERE elem ILIKE '%' || :result || '%'
                        )
                    )
                AND (CAST(:beginTime AS timestamp) IS NULL OR ( r.start_time BETWEEN :beginTime  AND :endTime ))
            """,
            nativeQuery = true
    )
    Page<ReportInfoDto> searchReportInfoNative(
            List<Integer> ids,
            String product,
            String version,
            Integer corpusId,
            String taskId,
            String taskSetName,
            String feature,
            String subFeature,
            String scene,
            String secondaryScene,
            String thirdScene,
            String userCorpus,
            String type,
            String activity,
            String result,
            Boolean hasSurveyScore,
            LocalDateTime beginTime,
            LocalDateTime   endTime,
            Pageable pageable);


    @Query(value = """
                SELECT 
                r.id,
                r.corpus_id AS corpusId,
                r.task_id AS taskId,
                r.product,
                r.version,
                r.start_time AS startTime,
                r.end_time AS endTime,
                r.performance,
                r.reliability,
                r.result,
                r.functionality,
                r.survey_score AS surveyScore,
                c.feature AS feature ,
                c.sub_feature AS subFeature ,
                c.scene AS scene ,
                c.secondary_scene AS secondaryScene ,
                c.third_scene AS thirdScene ,
                c.level AS level ,
                c.user_corpus AS userCorpus ,
                c.type AS type ,
                r.task_set_name AS taskSetName 
            FROM survey.report r
            LEFT JOIN survey.corpus c ON r.corpus_id = c.id
            WHERE 
                (:product IS NULL OR r.product IN :product)
                AND (:scene IS NULL OR c.scene = :scene )
                AND r.survey_score IS NOT NULL
    """,nativeQuery = true)
    List<ReportInfoDto> queryReportListByScene(String scene, List<String> product);




    @Query(value = """
            SELECT 
            r.product,avg(r.survey_score) as score
            FROM survey.report r
            LEFT JOIN survey.corpus c ON r.corpus_id = c.id
            WHERE 
                (:product IS NULL OR r.product IN :product)
                AND (:scene IS NULL OR c.scene = :scene )
                AND (:activity IS NULL OR r.activity = :activity )
                AND r.survey_score IS NOT NULL
            group by r.product
    """,nativeQuery = true)
    List<ProductScoreDto> queryProductScoreByScene(String scene, String activity,List<String> product);


    @Query(value = """
            SELECT 
            r.product,avg(r.survey_score) as score
            FROM survey.report r
            WHERE 
                (r.corpus_id = :corpusId)
                AND (:activity IS NULL OR r.activity IN :activity )
                AND r.survey_score IS NOT NULL
            group by r.product
    """,nativeQuery = true)
    List<ProductScoreDto> queryProductScoreByCorpusId(Integer corpusId,List<String> activity);


    @Query(value = """
            SELECT 
            distinct r.product
            FROM survey.report r
            LEFT JOIN survey.corpus c ON r.corpus_id = c.id
            WHERE 
                (:activity IS NULL OR r.activity = :activity)
                AND (:feature IS NULL OR c.feature = :feature)
    """,nativeQuery = true)
    List<String> queryProductList(String activity, String feature);


    @Query(value = """
            SELECT DISTINCT ON (r.corpus_id)
                r.corpus_id as corpusId,
                c.feature AS feature,
                c.sub_feature AS subFeature,
                c.scene AS scene,
                c.secondary_scene AS secondaryScene,
                c.third_scene AS thirdScene,
                c.user_corpus AS userCorpus,
                c.attachment AS attachment,
                r.task_set_name AS taskSetName,
                r.activity AS activity
            FROM survey.report r
            LEFT JOIN survey.corpus c ON r.corpus_id = c.id
            WHERE 
                (:feature IS NULL OR c.feature IN :feature)
                AND (:activity IS NULL OR r.activity IN :activity)
    """,
            countQuery = """
            SELECT COUNT(1)  
            FROM survey.report r
            LEFT JOIN survey.corpus c ON r.corpus_id = c.id
            WHERE   (:feature IS NULL OR c.feature IN :feature)
                AND (:activity IS NULL OR r.activity IN :activity) group by r.corpus_id""",
            nativeQuery = true)
    Page<CorpusStatsDto> queryCorpusStatsByFeatureAndActivity(List<String> feature, List<String> activity, Pageable pageable);


    @Modifying
    @Transactional
    @Query(value = """
        DELETE FROM survey.report
        WHERE activity = :activity
          AND product = :product
          AND corpus_id = :corpusId
          AND version = :version
        """, nativeQuery = true)
    void deleteByCondition(@Param("activity") String activity,
                           @Param("product") String product,
                           @Param("corpusId") Integer corpusId,
                           @Param("version") String version);


    @Query(value = """
            SELECT 
            distinct r.product
            FROM survey.report r
            LEFT JOIN survey.corpus c ON r.corpus_id = c.id
            WHERE
             (:feature IS NULL OR c.feature IN :feature)
            AND (:activity IS NULL OR r.activity IN :activity)
    """,nativeQuery = true)
    List<String> queryCorpusProductList(List<String> feature, List<String> activity);

    @Query(value = """
            SELECT 
            distinct r.activity
            FROM survey.report r
            where r.activity is not null
    """,nativeQuery = true)
    List<String> queryActivityList();


    @Query(value = """
            SELECT 
            r.product as product,
            r.result as result,
            r.survey_score as  score
        FROM survey.report r
        WHERE r.corpus_id = :corpusId
          AND  r.activity = :activity
    """, nativeQuery = true)
    List<CorpusProductResultDto> corpusProductResult(Integer corpusId, String activity);

    @Transactional
    @Modifying
    @Query(value = """
        update survey.report 
        set survey_score = :score,
            is_revise = 1
        WHERE id = :id
    """, nativeQuery = true)
    Integer updateSurveyScore(Integer id, Double score);


    @Query(value = """
        select
         r.activity,r.corpus_id
         from
            ( SELECT
             r.activity,r.corpus_id,max(r.create_time ) as time
             FROM survey.report r
             where
             (
                 :result IS NULL OR EXISTS (
                     SELECT 1 FROM jsonb_array_elements_text(r.result) AS elem
                     WHERE elem ILIKE '%' || :result || '%'
                 )
             )
            AND ( :product IS NULL or r.product = :product)
             group by r.activity, r.corpus_id ) r
         join survey.corpus c on r.corpus_id = c.id
         where 
             ( :scene IS NULL or c.scene = :scene)
         AND ( :secondaryScene IS NULL or c.secondary_scene = :secondaryScene)
         AND ( :activity IS NULL or r.activity = :activity)
         AND ( :feature IS NULL or c.feature = :feature)
         AND ( :subFeature IS NULL or c.sub_feature = :subFeature)
    """, countQuery = """
        select
         count(1)
         from
            ( SELECT
             r.activity,r.corpus_id,max(r.create_time ) as time
             FROM survey.report r
             where
             (
                 :result IS NULL OR EXISTS (
                     SELECT 1 FROM jsonb_array_elements_text(r.result) AS elem
                     WHERE elem ILIKE '%' || :result || '%'
                 )
             )
             AND ( :product IS NULL or r.product = :product)
             group by r.activity, r.corpus_id ) r
         join survey.corpus c on r.corpus_id = c.id
         where 
             ( :scene IS NULL or c.scene = :scene)
             AND ( :secondaryScene IS NULL or c.secondary_scene = :secondaryScene)
             AND ( :activity IS NULL or r.activity = :activity)
             AND ( :feature IS NULL or c.feature = :feature)
            AND ( :subFeature IS NULL or c.sub_feature = :subFeature)
    """,
    nativeQuery = true)
    Page<ActivityCorpusDto> queryAllCorpusId(String feature,String subFeature, String product,String activity ,String scene,String secondaryScene,String result,Pageable pageable);

    @Query(value = """
        SELECT *
        FROM survey.report r
        WHERE r.corpus_id = :corpusId
          AND  r.activity = :activity
        AND r.product IN :productList
    """, nativeQuery = true)
    List<Report> queryByActivityAndCorpusIdAndProduct(String activity, Integer corpusId, List<String> productList);


    @Query(value = """
        SELECT DISTINCT c.feature
        FROM survey.report r
        JOIN survey.corpus c ON r.corpus_id = c.id
        WHERE r.activity = :activity
    """, nativeQuery = true)
    List<String> queryFeatureList(String activity);

}

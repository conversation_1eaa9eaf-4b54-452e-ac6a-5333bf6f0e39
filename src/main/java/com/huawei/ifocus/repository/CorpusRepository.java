package com.huawei.ifocus.repository;

import com.huawei.ifocus.entity.Corpus;

import jakarta.transaction.Transactional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CorpusRepository extends
        PagingAndSortingRepository<Corpus, Integer>,
        JpaRepository<Corpus, Integer>,
        JpaSpecificationExecutor<Corpus> {


    @Query(value = """
                SELECT *
                FROM survey.corpus
                WHERE 
                    EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements_text(user_corpus) AS elem
                    WHERE elem ILIKE :pattern
                )
            """, nativeQuery = true)
    List<Corpus> searchByUserCorpusLike(@Param("pattern") String pattern);


    @Query(value = """
            SELECT * FROM survey.corpus
            WHERE 
                (:feature IS NULL OR feature = :feature)
                AND (:secondaryScene IS NULL OR secondary_scene ILIKE '%' || :secondaryScene || '%')
                AND (:createUser IS NULL OR create_user ILIKE '%' || :createUser || '%'  )
                AND (:thirdScene IS NULL OR third_scene  ILIKE '%' || :thirdScene || '%' )
                AND (:scene IS NULL OR scene ILIKE '%' || :scene || '%' )
                AND (:subFeature IS NULL OR sub_feature ILIKE '%' || :subFeature || '%')
                AND (:id IS NULL OR id = :id)
                AND (:source IS NULL OR source = :source)
                AND (:level IS NULL OR level = :level)
                AND (
                    :userCorpus IS NULL OR EXISTS (
                        SELECT 1 FROM jsonb_array_elements_text(user_corpus) AS elem
                        WHERE elem ILIKE '%' || :userCorpus || '%'
                    )
                )
            """,
            nativeQuery = true)
    Page<Corpus> searchCorpusNative(
            @Param("feature") String feature,
            @Param("secondaryScene") String secondaryScene,
            @Param("createUser") String createUser,
            @Param("thirdScene") String thirdScene,
            @Param("scene") String scene,
            @Param("subFeature") String subFeature,
            @Param("userCorpus") String userCorpus,
             String source,
             String level,
             Integer id,
            Pageable pageable
    );

    @Query(value = """
            SELECT 
            distinct c.scene
            FROM survey.report r
            LEFT JOIN survey.corpus c ON r.corpus_id = c.id
            WHERE 
                (:activity IS NULL OR r.activity = :activity)
                AND (:feature IS NULL OR c.feature = :feature)
    """,nativeQuery = true)
    List<String> queryAllSceneByFeature(String feature,String activity);

    @Query(value = """
            SELECT DISTINCT feature
            FROM survey.corpus
            """, nativeQuery = true)
    List<String> queryFeatureList();


    @Modifying
    @Transactional
    @Query(value = """
            update 
            survey.corpus
            set count = count + 1
            where id =:corpusId
            """, nativeQuery = true)
    void updateCount(Integer corpusId);
}

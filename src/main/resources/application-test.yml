# devops test 环境
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *********************************************
    username: <PERSON><PERSON>(j2c.db_user.username)
    password: <PERSON><PERSON>(j2c.db_user.password)
    hikari:
      pool-name: HikariPool-report-featuredb
      maximum-pool-size: 50
      minimum-idle: 1
      connection-timeout: 10000
      max-lifetime: 900000
      idle-timeout: 450000
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        default_schema: survey
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
eureka:
  instance:
    prefer-ip-address: true
  client:
    serviceUrl:
      defaultZone: http://kweuat.huawei.com/msa/register/v2 # 贵州 UAT 及体验环境


aws:
  s3:
    accessKey: ENC(j2c.s3_ak.password)
    secretKey: ENC(j2c.s3_sk.password)
    endPoint: http://obs.cn-southwest-260.myhuaweicloud.com
    pathStyleAccessEnabled: false
    bucket-name: ifocus-s3
    path: survey/



beta:
  j2c:
    enabled: true
    ########### 配置中心配置获取url
    # soa 授权域名： 通过app-id和静态token通过该域名获取动态token； 正式环境为 http://oauth2.huawei.com
    auth-server: http://oauth2-beta.huawei.com
    # 配置中心配置获取url,正式环境为  http://appconfig.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig
    config-center-url: http://appconfig-beta.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig

    ########### 获取部署单元配置参数部分
    app-id: ${his_app_id}
    deploy-unit: ${pcloud_subapp_name}
    environment: ${docker_env}
    region: ${beta_j2c_region}
    version: ${beta_j2c_version}

    ############ 应用静态token加密部分
    cipher-text: ${beta_j2c_cipherText}
    work-key-cipher: ${beta_j2c_workKeyCipher}
    config-parts:
      - ${beta_j2c_configParts1}
      - ${beta_j2c_configParts2}

message:
  url: http://feed.cloud.huawei.com/feedmsg/publicservices/template/sendTemplateMessage
  app-id: S00000000000000000000000000003559
  theme-id: 61481449e1b04334af4a9b53cce6d338
  jump-url: https://ifocus.taas.huawei.com/home/<USER>/ai-testing?path=platform,{activityType}&id=
  template-no: 202502111998

logging:
  file:
    path: /applog/logs
    name: iFocusSurvey.log
  level:
    root: debug


server:
  port: 9205


his:
  ifocus:
    appid: S00000000000000000000000000003559
    app-key: 2IGLIpXQHacIPtVERMMNCw==
    dts-username: pub_publicifocus
    hci_auth_token: 805a5084-e2e6-41c2-b45a-bfdf871eb9a7
    token: YyUKzFGNSvVID4I_B8Bj0Xm5ZJgnIy-ybcl9QvAVMFFJAKXSBweWa9lmX95QT6Q0up2-PSewvWNChbmlDiuXaA
  url:
    dts-url: http://apigw.dragon.tools.huawei.com/api/biportal/v1/getTicketByDtsNo
    hci_cookie: http://hites.rnd.huawei.com/hids/auth/doLogin
    hci_task_result: http://hites.rnd.huawei.com/feat/measure/aresact/testData/getTaskCaseResult

login-server:
  local-server-name: autotest
  host: handset-test.huawei.com
  port: 8801
  manager: XX XX # 权限管理员，用于网站权限不足时弹出提示

url:
  xml-compare: http://**************:8080/compare
  ifocus-cookie: http://ifocus/taas.huawei.com/auth/cookie
  testland-correlation-dts: http://ifocus/taas.huawei.com/testland/caseTask/correlationDts

api:
  auth:
    static-token: YyUKzFGNSvVID4I_B8Bj0Xm5ZJgnIy-ybcl9QvAVMFFJAKXSBweWa9lmX95QT6Q0up2-PSewvWNChbmlDiuXaA
    account: S00000000000000000000000000003559
    appid: S00000000000000000000000000003559
    tenant: huawei
    endpoints: http://oauth2.huawei.com





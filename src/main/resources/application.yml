spring:
  application:
    name: S00000000000000000000000000003559-survey
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        default_schema: survey
        dialect: org.hibernate.dialect.PostgreSQLDialect
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
logging:
  file:
    path: ./logs
    name: iFocusSurvey.log
  operation:
    path: ./applog/iFocusSurvey/log
    name: operation.log

eureka:
  instance:
    hostname: ************
  client:
    register-with-eureka: true
    fetch-registry: true
    serviceUrl:
      defaultZone: http://************:9000/eureka/

his:
  ifocus:
    appid: S00000000000000000000000000003559
    app-key: 2IGLIpXQHacIPtVERMMNCw==
    dts-username: pub_publicifocus
    hci_auth_token: 805a5084-e2e6-41c2-b45a-bfdf871eb9a7
    token: YyUKzFGNSvVID4I_B8Bj0Xm5ZJgnIy-ybcl9QvAVMFFJAKXSBweWa9lmX95QT6Q0up2-PSewvWNChbmlDiuXaA
  url:
    dts-url: http://apigw.dragon.tools.huawei.com/api/biportal/v1/getTicketByDtsNo
    hci_cookie: http://hites.rnd.huawei.com/hids/auth/doLogin
    hci_task_result: http://hites.rnd.huawei.com/feat/measure/aresact/testData/getTaskCaseResult
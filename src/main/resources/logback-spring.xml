<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="logging.path"  source="logging.file.path"/>
    <springProperty scope="context" name="logging.file"  source="logging.file.name"/>
    <springProperty scope="context" name="logging.operation.path" source="logging.operation.path"/>
    <springProperty scope="context" name="logging.operation.file" source="logging.operation.name"/>
    <!-- <springProperty scope="context" name="logging.level" source="logging.level.../>-->

   <!-- 默认的控制台日志输出 -->
   <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
       <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
           <Pattern>%d{HH:mm:ss.SSS}  %-5level  [%thread] [%logger{80} %line] - %msg%n</Pattern>
       </encoder>
   </appender>

    <appender name="file-logger-operation" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <file>
            ${logging.operation.path}/${logging.operation.file}
        </file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logging.operation.path}/${logging.operation.file}.%d{yyyy-MM-dd}.log</FileNamePattern>
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5p --- %-50.50logger{50} : %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

   <appender name="file-logger" class="ch.qos.logback.core.rolling.RollingFileAppender">
       <append>true</append>
       <!-- <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
           <level>${logging.level}</level>
       </filter> -->
       <file>
           ${logging.path}/${logging.file}
       </file>
       <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
           <FileNamePattern>${logging.path}/${logging.file}.%d{yyyy-MM-dd}.log</FileNamePattern>
           <MaxHistory>90</MaxHistory>
       </rollingPolicy>
       <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
           <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5p --- %-50.50logger{50} : %msg%n</pattern>
           <charset>UTF-8</charset>
       </encoder>
   </appender>

    <logger name="com.huawei.ifocus.common.aop.OperationLogRecord" level="info">
        <appender-ref ref="file-logger-operation"/>
    </logger>


    <!--    <logger name="com.huawei.ifocus" level="debug" additivity="false">-->
<!--        <appender-ref ref="console" />-->
<!--        <appender-ref ref="file-logger" />-->
<!--    </logger>-->

   <root level="info" >
       <appender-ref ref="file-logger"/>
       <appender-ref ref="console" />
   </root>

</configuration>
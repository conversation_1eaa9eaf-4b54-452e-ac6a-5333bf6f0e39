# 9000 环境
server:
  port: 9205
message:
  url: http://feed.cloud.huawei.com/feedmsg/publicservices/template/sendTemplateMessage
  app-id: S00000000000000000000000000003559
  theme-id: 61481449e1b04334af4a9b53cce6d338
  jump-url: http://ifocus.huawei.com:9000/ifocus/testExecution/ai-testing?id=
  template-no: 202502111998
spring:
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        default_schema: survey
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
  datasource:
    driver-class-name: org.postgresql.Driver
    url: **********************************************
    username: hci
    password: hci...123
    hikari:
      pool-name: HikariPool-report-featuredb
      maximum-pool-size: 50
      minimum-idle: 1
      connection-timeout: 10000
      max-lifetime: 900000
      idle-timeout: 450000
aws:
  s3:
    pathStyleAccessEnabled: true
    bucket-name: ifocus-s3
    path: survey/
    accessKey: HPUARXQKNPJDD46GCQNM
    secretKey: DFRuHqnzDV5rjhvmRzZauijmUPC1MzmLarqKUDlo
    endPoint: http://s3-kwe203.his-beta.huawei.com

eureka:
  instance:
    hostname: ************
    # prefer-ip-address: true
    # ip-address: ************
  client:
    register-with-eureka: true
    fetch-registry: true
    serviceUrl:
      defaultZone: http://************:9000/eureka/
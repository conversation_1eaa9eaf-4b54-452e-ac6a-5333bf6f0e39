server:
  port: 9207
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
#    url: jdbc:postgresql://*************:5432/ifocusdb
#    username: <PERSON><PERSON>(j2c.db_user.username)
#    password: <PERSON><PERSON>(j2c.db_user.password)
    url: **********************************************
    username: hci
    password: hci...123
    hikari:
      pool-name: HikariPool-survey
      maximum-pool-size: 50
      minimum-idle: 1
      connection-timeout: 10000
      max-lifetime: 900000
      idle-timeout: 450000
beta:
  j2c:
    enabled: true
    ########### 配置中心配置获取url
    # soa 授权域名： 通过app-id和静态token通过该域名获取动态token； 正式环境为 http://oauth2.huawei.com
    auth-server: http://oauth2-beta.huawei.com
    # 配置中心配置获取url,正式环境为  http://appconfig.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig
    config-center-url: http://appconfig-beta.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig

    ########### 获取部署单元配置参数部分
    app-id: ${his_app_id}
    deploy-unit: ${pcloud_subapp_name}
    environment: ${docker_env}
    region: ${beta_j2c_region}
    version: ${beta_j2c_version}

    ############ 应用静态token加密部分
    cipher-text: ${beta_j2c_cipherText}
    work-key-cipher: ${beta_j2c_workKeyCipher}
    config-parts:
      - ${beta_j2c_configParts1}
      - ${beta_j2c_configParts2}



message:
  url: http://feed.cloud.huawei.com/feedmsg/publicservices/template/sendTemplateMessage
  app-id: S00000000000000000000000000003559
  theme-id: 61481449e1b04334af4a9b53cce6d338
  jump-url: http://localhost.huawei.com:9004/ifocus/testExecution/ai-testing?path=platform,{activityType}&id=
  template-no: 202502111998
aws:
  s3:
    pathStyleAccessEnabled: true
    bucket-name: ifocus-s3
    path: survey/
    accessKey: HPUARXQKNPJDD46GCQNM
    secretKey: DFRuHqnzDV5rjhvmRzZauijmUPC1MzmLarqKUDlo
    endPoint: http://s3-kwe203.his-beta.huawei.com


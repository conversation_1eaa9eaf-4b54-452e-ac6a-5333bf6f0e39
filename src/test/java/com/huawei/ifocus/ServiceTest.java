package com.huawei.ifocus;

import com.huawei.ifocus.entity.Report;
import com.huawei.ifocus.repository.*;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

@SpringBootTest
@ExtendWith(MockitoExtension.class)
@Slf4j
@ActiveProfiles("local")
public class ServiceTest {

    @Autowired
    JdbcTemplate jdbcTemplate;

    @Autowired
    ActivityRepository activityRepository;

    @Autowired
    ReportRepository reportRepository;

    @Autowired
    ActivityFeedBackRepository activityFeedBackRepository;


    @Autowired
    ActivityMaterialRepository activityMaterialRepository;
    @Autowired
    ReportJdbcRepository reportJdbcRepository;
    @Autowired
    private CorpusRepository corpusRepository;


    @Test
    public void testQuery() {
        Integer activityId = 25;
        List<Report> reports = reportRepository.queryByActivityAndCorpusIdAndProduct("234", 3117, Lists.newArrayList("123"));

        List<String> strings1 = reportJdbcRepository.querySceneList("123","VDE-AL10_S");
        List<String> secondarySceneList = reportJdbcRepository.querySecondarySceneList("问答","123","VDE-AL10_S");
        List<Integer> secondarySceneList2 = reportJdbcRepository.queryCorpusIdBySelfProductWin("123","知识问答 —准确性","VDE-AL10_S","PKC110");






        List<String> strings = activityFeedBackRepository.queryTaskIdListByActivityId(12);

        corpusRepository.updateCount(20);

        Integer l = reportJdbcRepository.countSelfProductWinScene("问答","123", "VDE-AL10_S", "PKC110");
        Integer l2 = reportJdbcRepository.countCompetingProductWinByScene("问答","123","VDE-AL10_S", "PKC110" );
        Double l3 = reportJdbcRepository.calculateAvgByScene("问答","123","VDE-AL10_S");








        List<String> windowNames = activityFeedBackRepository.queryWindowNameByActivityId(activityId);
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT f.activity_id, m.user_corpus, f.provider");
        for (String window : windowNames) {
            sql.append(", MAX(CASE WHEN f.window_name = '")
                    .append(window)
                    .append("' THEN f.score END) AS \"")
                    .append(window)
                    .append("\"");
        }
        sql.append(" FROM survey.activity_material m ")
                .append("JOIN survey.activity_feedback f ")
                .append("ON m.activity_id = f.activity_id AND m.group_index = f.group_index ")
                .append("WHERE m.activity_id = ")
                .append(activityId)
                .append(" GROUP BY f.activity_id, m.user_corpus, f.provider");

        List<Map<String, Object>> res = jdbcTemplate.queryForList(sql.toString());


    }


    public static void main(String[] args) {
    }
}

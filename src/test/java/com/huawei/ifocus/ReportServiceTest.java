package com.huawei.ifocus;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.huawei.ifocus.dto.ActivityCorpusDto;
import com.huawei.ifocus.dto.CorpusReportInfo;
import com.huawei.ifocus.entity.Corpus;
import com.huawei.ifocus.entity.Report;
import com.huawei.ifocus.repository.CorpusRepository;
import com.huawei.ifocus.repository.ReportRepository;
import com.huawei.ifocus.service.ReportService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

@ExtendWith(MockitoExtension.class)
class ReportServiceTest {

    @Mock
    private CorpusRepository corpusRepository;

    @Mock
    private ReportRepository reportRepository;

    @InjectMocks
    private ReportService reportService;

    @Test
    void queryCorpusProductResult_ReturnsCorrectResults_WhenValidInputProvided() {
        List<ActivityCorpusDto> corpusIdList = List.of(new ActivityCorpusDto( "Activity1",1));
        List<String> productList = List.of("Product1", "Product2");

        Corpus corpus = new Corpus();
        corpus.setId(1);
        corpus.setScene("Corpus1");

        Report report1 = new Report();
        report1.setProduct("Product1");
        Report report2 = new Report();
        report2.setProduct("Product2");

        when(corpusRepository.findById(1)).thenReturn(Optional.of(corpus));
        when(reportRepository.queryByActivityAndCorpusIdAndProduct("Activity1", 1, productList))
                .thenReturn(List.of(report1, report2));

        List<CorpusReportInfo> result = reportService.queryCorpusProductResult(corpusIdList, productList);

        assertEquals(1, result.size());
        assertEquals("Activity1", result.get(0).getActivity());
        assertEquals(1, result.get(0).getCorpusId());
        assertEquals(2, result.get(0).getProductScore().size());
        assertTrue(result.get(0).getProductScore().containsKey("Product1"));
        assertTrue(result.get(0).getProductScore().containsKey("Product2"));
    }

    @Test
    void queryCorpusProductResult_ReturnsEmptyList_WhenCorpusIdListIsEmpty() {
        List<ActivityCorpusDto> corpusIdList = Collections.emptyList();
        List<String> productList = List.of("Product1", "Product2");

        List<CorpusReportInfo> result = reportService.queryCorpusProductResult(corpusIdList, productList);

        assertTrue(result.isEmpty());
    }

    @Test
    void queryCorpusProductResult_ThrowsException_WhenCorpusNotFound() {
        List<ActivityCorpusDto> corpusIdList = List.of(new ActivityCorpusDto("Activity1",1));
        List<String> productList = List.of("Product1", "Product2");

        when(corpusRepository.findById(1)).thenReturn(Optional.empty());

        assertThrows(NoSuchElementException.class, () -> reportService.queryCorpusProductResult(corpusIdList, productList));
    }

    @Test
    void queryCorpusProductResult_ReturnsEmptyProductScore_WhenNoReportsFound() {
        List<ActivityCorpusDto> corpusIdList = List.of(new ActivityCorpusDto("Activity1",1));
        List<String> productList = List.of("Product1", "Product2");

        Corpus corpus = new Corpus();
        corpus.setId(1);
        corpus.setScene("Corpus1");

        when(corpusRepository.findById(1)).thenReturn(Optional.of(corpus));
        when(reportRepository.queryByActivityAndCorpusIdAndProduct("Activity1", 1, productList))
                .thenReturn(Collections.emptyList());

        List<CorpusReportInfo> result = reportService.queryCorpusProductResult(corpusIdList, productList);

        assertEquals(1, result.size());
        assertTrue(result.get(0).getProductScore().isEmpty());
    }
}
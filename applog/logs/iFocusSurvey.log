2025-02-13 09:21:32.902 [background-preinit] INFO  --- org.hibernate.validator.internal.util.Version      : HV000001: Hibernate Validator 8.0.1.Final
2025-02-13 09:21:32.920 [main] INFO  --- com.huawei.ifocus.IFocusSurveyApplication          : Starting IFocusSurveyApplication using Java 17.0.13 with PID 82552 (D:\work\code\ifocus-survey\target\classes started by q30069056 in D:\work\code\ifocus-survey)
2025-02-13 09:21:32.921 [main] INFO  --- com.huawei.ifocus.IFocusSurveyApplication          : The following 1 profile is active: "local"
2025-02-13 09:21:33.247 [main] INFO  --- o.s.d.r.config.RepositoryConfigurationDelegate     : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-02-13 09:21:33.274 [main] INFO  --- o.s.d.r.config.RepositoryConfigurationDelegate     : Finished Spring Data repository scanning in 23 ms. Found 4 JPA repository interfaces.
2025-02-13 09:21:33.497 [main] WARN  --- org.mybatis.spring.mapper.ClassPathMapperScanner   : No MyBatis mapper was found in '[com.huawei.ifocus]' package. Please check your configuration.
2025-02-13 09:21:33.592 [main] INFO  --- nableEncryptablePropertiesBeanFactoryPostProcessor : Post-processing PropertySource instances
2025-02-13 09:21:33.592 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-02-13 09:21:33.592 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-02-13 09:21:33.592 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-02-13 09:21:33.593 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-02-13 09:21:33.593 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-02-13 09:21:33.593 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-02-13 09:21:33.593 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Converting PropertySource Config resource 'class path resource [application-local.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-02-13 09:21:33.593 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-02-13 09:21:33.755 [main] INFO  --- c.u.j.filter.DefaultLazyPropertyFilter             : Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-02-13 09:21:33.764 [main] INFO  --- c.u.j.resolver.DefaultLazyPropertyResolver         : Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-02-13 09:21:33.765 [main] INFO  --- c.u.j.detector.DefaultLazyPropertyDetector         : Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-02-13 09:21:33.866 [main] INFO  --- o.s.boot.web.embedded.tomcat.TomcatWebServer       : Tomcat initialized with port(s): 9206 (http)
2025-02-13 09:21:33.870 [main] INFO  --- org.apache.coyote.http11.Http11NioProtocol         : Initializing ProtocolHandler ["http-nio-9206"]
2025-02-13 09:21:33.870 [main] INFO  --- org.apache.catalina.core.StandardService           : Starting service [Tomcat]
2025-02-13 09:21:33.871 [main] INFO  --- org.apache.catalina.core.StandardEngine            : Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-02-13 09:21:33.919 [main] INFO  --- o.a.c.c.C.[Tomcat].[localhost].[/survey]           : Initializing Spring embedded WebApplicationContext
2025-02-13 09:21:33.919 [main] INFO  --- o.s.b.w.s.c.ServletWebServerApplicationContext     : Root WebApplicationContext: initialization completed in 975 ms
2025-02-13 09:21:34.061 [main] INFO  --- org.hibernate.jpa.internal.util.LogHelper          : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-02-13 09:21:34.088 [main] INFO  --- org.hibernate.Version                              : HHH000412: Hibernate ORM core version 6.2.13.Final
2025-02-13 09:21:34.089 [main] INFO  --- org.hibernate.cfg.Environment                      : HHH000406: Using bytecode reflection optimizer
2025-02-13 09:21:34.218 [main] INFO  --- o.s.o.j.persistenceunit.SpringPersistenceUnitInfo  : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-02-13 09:21:34.232 [main] INFO  --- com.zaxxer.hikari.HikariDataSource                 : HikariPool-report-featuredb - Starting...
2025-02-13 09:21:34.336 [main] INFO  --- com.zaxxer.hikari.pool.HikariPool                  : HikariPool-report-featuredb - Added connection org.postgresql.jdbc.PgConnection@ef8a16
2025-02-13 09:21:34.336 [main] INFO  --- com.zaxxer.hikari.HikariDataSource                 : HikariPool-report-featuredb - Start completed.
2025-02-13 09:21:34.357 [main] WARN  --- org.hibernate.orm.deprecation                      : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-02-13 09:21:34.863 [main] INFO  --- o.h.e.t.jta.platform.internal.JtaPlatformInitiator : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-02-13 09:21:34.864 [main] INFO  --- o.s.orm.jpa.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-02-13 09:21:35.312 [main] WARN  --- s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-02-13 09:21:36.091 [main] INFO  --- o.s.b.actuate.endpoint.web.EndpointLinksResolver   : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-02-13 09:21:36.125 [main] INFO  --- org.apache.coyote.http11.Http11NioProtocol         : Starting ProtocolHandler ["http-nio-9206"]
2025-02-13 09:21:36.136 [main] INFO  --- o.s.boot.web.embedded.tomcat.TomcatWebServer       : Tomcat started on port(s): 9206 (http) with context path '/survey'
2025-02-13 09:21:36.137 [main] INFO  --- c.u.j.caching.RefreshScopeRefreshedEventListener   : Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-02-13 09:21:36.137 [main] INFO  --- c.u.j.c.CachingDelegateEncryptablePropertySource   : Property Source systemProperties refreshed
2025-02-13 09:21:36.137 [main] INFO  --- c.u.j.c.CachingDelegateEncryptablePropertySource   : Property Source systemEnvironment refreshed
2025-02-13 09:21:36.137 [main] INFO  --- c.u.j.c.CachingDelegateEncryptablePropertySource   : Property Source random refreshed
2025-02-13 09:21:36.138 [main] INFO  --- c.u.j.c.CachingDelegateEncryptablePropertySource   : Property Source Config resource 'class path resource [application-local.yml]' via location 'optional:classpath:/' refreshed
2025-02-13 09:21:36.138 [main] INFO  --- c.u.j.c.CachingDelegateEncryptablePropertySource   : Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
2025-02-13 09:21:36.138 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-02-13 09:21:36.138 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-02-13 09:21:36.138 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-02-13 09:21:36.138 [main] INFO  --- c.u.j.EncryptablePropertySourceConverter           : Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-02-13 09:21:36.147 [main] INFO  --- com.huawei.ifocus.IFocusSurveyApplication          : Started IFocusSurveyApplication in 3.474 seconds (process running for 3.786)
2025-02-13 09:52:38.357 [SpringApplicationShutdownHook] INFO  --- o.s.orm.jpa.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-02-13 09:52:38.358 [SpringApplicationShutdownHook] INFO  --- com.zaxxer.hikari.HikariDataSource                 : HikariPool-report-featuredb - Shutdown initiated...
2025-02-13 09:52:38.359 [SpringApplicationShutdownHook] INFO  --- com.zaxxer.hikari.HikariDataSource                 : HikariPool-report-featuredb - Shutdown completed.

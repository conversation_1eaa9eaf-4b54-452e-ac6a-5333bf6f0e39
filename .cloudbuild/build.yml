---
version: 2.0
#构建环境
env: #了解如何进行环境配置 http://cloudbuild-doc.inhuawei.com/chapter3/section24.html
  resource:
    type: docker
#    image: kweecr05.his.huawei.com:80/ecr-build-green202/docker_eulerv200r012b150_x86_gy202g_builduser:202411151719
    image: kweecr04.his.huawei.com:80/ecr-build-arm-gzkunpeng/ifcous_build_jdk17:v0.0.2
    mode: toolbox
#    pool: docker-gz-x86-ondocker-16u-01
  cache:
    - type: maven
      path: /devcloud/maven/repository

#构建参数定义, 构建脚本可从环境变量中读取使用这些参数
params: #了解如何配置构建参数http://cloudbuild-doc.inhuawei.com/chapter3/parameters.html
  - name: CB_AUTO_CHECK_VERSION
    value: 2.0
  - name: BC_NEXT3RD_VERSION # 指定版本 推荐配置
    value: 1.1.95 # 请根据ReleaseNote选择最新版本
#构建步骤
steps: #了解如何配置构建步骤：http://cloudbuild-doc.inhuawei.com/chapter3/section25.html
  PRE_BUILD: #了解如何配置构建准备阶段：http://cloudbuild-doc.inhuawei.com/chapter3/section26.html
    - checkout #检出当前源码库
  BUILD: #了解如何配置构建执行步骤：http://cloudbuild-doc.inhuawei.com/chapter3/plugins-in-build-stage.html
    - build_execute: #执行构建
        command: sh .cloudbuild/build.sh #构建命令，如sh build.sh 或 make 或 mvn clean package
        accelerate: false #是否启用分布式加速(jiffy)
        check:
          auto: true  # CB_AUTO_CHECK_VERSION=2.0 且 auto=true 时，开启自动模式的依赖分析
          callstack: false
          buildcheck: true
          fail_on_error: true
          dangerouscmd: true
  POST_BUILD:  #了解如何配置构建后步骤：http://cloudbuild-doc.inhuawei.com/chapter3/plugins-in-postbuild-stage.html
    - artget: #从云龙上传构建cloudArtifact仓库 需要云龙流水线传入serviceId,serviceName,isRelease参数
        artifact_type: edevops_artifact  # 仓库类型
        file_path: tar_target/*.tar.gz
    - compile_report:
        compiler: javac
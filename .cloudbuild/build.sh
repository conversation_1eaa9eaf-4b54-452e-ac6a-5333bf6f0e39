#!/bin/bash

# Define the source and destination directories
SOURCE_DIR="target"
DEPLOY_DIR="deploy"
TARGET_DIR="tar_target"

# 打包
echo "Running Maven clean package..."
#export JAVA_HOME=/opt/buildtools/adoptopenjdk17-jdk-17.0.11+9
#export PATH=$JAVA_HOME/bin:$PATH
#export CLASSPATH=.:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar
mvn clean package \
-Dmaven.test.skip=true \
-Dmaven.wagon.http.ssl.insecure=true \
-Dmaven.wagon.http.ssl.allowall=true \
-Dmaven.wagon.http.ignore.validity.dates=true \
-gs ./settings.xml -s ./settings.xml

# Check if the <PERSON>ven command was successful
if [ $? -ne 0 ]; then
    echo "Maven build failed. Exiting."
    exit 1
fi

# Find the JAR file in the source directory
JAR_FILE=$(find "$SOURCE_DIR" -name "*.jar" | head -n 1)
# Check if the JAR file exists
if [ -z "$JAR_FILE" ]; then
    echo "No JAR file found in $SOURCE_DIR"
    exit 1
fi

# 将jar包文件复制至deploy目录下
TARGET_LIBS_DIR="${DEPLOY_DIR}/libs"
if [[ ! -d ${TARGET_LIBS_DIR} ]]; then
  echo "create new libs_path : ${TARGET_LIBS_DIR}"
  mkdir -p ${TARGET_LIBS_DIR}
fi
mv ${JAR_FILE} ${TARGET_LIBS_DIR}/app.jar

# tar包
BASENAME=$(basename "$JAR_FILE")
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
DEPLOY_NAME="${BASENAME%.jar}_${TIMESTAMP}.tar.gz"
echo "${BASENAME}"
echo "${DEPLOY_NAME}"
tar zcvf ${DEPLOY_NAME} ${DEPLOY_DIR}/*

mkdir -p ${TARGET_DIR}
mv ${DEPLOY_NAME} ${TARGET_DIR}

# Construct the new filename with the timestamp
#BASENAME=$(basename "$JAR_FILE")
#TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
#NEW_FILENAME="${BASENAME%.jar}_$TIMESTAMP.jar"

#echo "${BASENAME}"
#echo "${NEW_FILENAME}"
#echo "${SOURCE_DIR}/${NEW_FILENAME}"
#mv "${JAR_FILE}" "${SOURCE_DIR}/${NEW_FILENAME}"

## Copy the JAR file to the destination with the new filename
#mkdir -p DESTINATION_DIR
#cp "$JAR_FILE" "$DESTINATION_DIR/$NEW_FILENAME"
#
## Confirm the copy operation
#if [ $? -eq 0 ]; then
#    echo "Copied $JAR_FILE to $DESTINATION_DIR/$NEW_FILENAME"
#else
#    echo "Failed to copy $JAR_FILE"
#    exit 1
#fi

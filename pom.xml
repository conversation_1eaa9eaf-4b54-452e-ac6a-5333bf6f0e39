<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.1</version>
        <relativePath/>
    </parent>
    <groupId>com.huawei.ifocus</groupId>
    <artifactId>iFocusSurvey</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>iFocusSurvey</name>
    <description>iFocus-Survey-service</description>
    <properties>
        <java.version>17</java.version>
        <auth-sdk.version>3.1.1</auth-sdk.version>
        <skipTests>true</skipTests>
        <spring-boot.version>3.4.1</spring-boot.version>
        <spring-boot.validation.version>3.4.1</spring-boot.validation.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
        <joda-time.version>2.13.1</joda-time.version>
        <xstream.version>1.4.21</xstream.version>
        <beta-new-encrypt.version>2.7.12.20231113</beta-new-encrypt.version>
        <!-- logback从spring-boot-dependencies中指定的1.5.2 -> 1.5.6 -->
        <logback.version>1.5.16</logback.version>
        <mybatis.plus.version>3.5.7</mybatis.plus.version>
        <apache.poi-ooxml.version>5.4.0</apache.poi-ooxml.version>
        <apache.commons.version>1.6.0</apache.commons.version>
        <tomcat.version>11.0.5</tomcat.version>
        <apache.httpclient.version>4.5.14</apache.httpclient.version>
        <fasterxml.jackson.version>2.18.2</fasterxml.jackson.version>
        <alibaba.fastjson.version>2.0.54</alibaba.fastjson.version>
        <knife4j.version>4.4.0</knife4j.version>
        <springdoc.version>2.8.5</springdoc.version>
        <hutool.version>5.8.29</hutool.version>
        <apache.httpclient.version>4.5.14</apache.httpclient.version>
        <xstream.version>1.4.21</xstream.version>
        <joda-time.version>2.13.1</joda-time.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
    </properties>
    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-jxpath</groupId>
                    <artifactId>commons-jxpath</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
            <version>6.5.0</version>
            <scope>compile</scope>
        </dependency>

        <!-- Tomcat单独依赖，避免产生漏洞 -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>tomcat-annotations-api</artifactId>
                    <groupId>org.apache.tomcat</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <version>${tomcat.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${apache.httpclient.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.glassfish.jersey</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda-time.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>${xstream.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>org.glassfish.jersey</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.5</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.17.0</version>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.18.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.53</version>
        </dependency>


        <!-- Swagger -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
            <version>4.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
            <version>2.8.5</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.8.5</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>


        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <scope>provided</scope>
        </dependency>

        <!--加密-->

        <dependency>
            <groupId>com.huawei.apic.client</groupId>
            <artifactId>auth-sdk-bundle-consumer</artifactId>
            <version>${auth-sdk.version}</version>
        </dependency>

        <!-- j2c  his配置中心对接依赖包及优选版本  -->
        <dependency>
            <groupId>com.huawei.betaclub</groupId>
            <artifactId>beta-new-encrypt</artifactId>
            <version>${beta-new-encrypt.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcutil-jdk15to18</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.apache.httpcomponents</artifactId>
                    <groupId>httpmime</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.kmssdk</groupId>
                    <artifactId>kmssdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.14</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.3</version>
        </dependency>


        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.12.375</version>
        </dependency>


    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <image>
                        <builder>paketobuildpacks/builder-jammy-base:latest</builder>
                    </image>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>

---
version: 2.0
tool_params:
  secsolar:
    compile_root_dir:
    compile_script: mvn clean install -Dmaven.test.skip=true -gs ./settings.xml -s ./settings.xml
    java_version: jdk17
    #mvn编译命令，setting文件相对于代码仓的根目录。
  sonarqube:
    sonarQubeScannerArgs: -Dsonar.java.binaries=./ -Dsonar.binaries=./ -Dsonar.sources=./ -Dsonar.java.source=17 -Dsonar.inclusions=**/*.java
    shellBeforeCheck: mvn clean install -Dmaven.test.skip=true -gs ./settings.xml -s ./settings.xml
#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.

set -ex
set -o pipefail

export BASE_DIR=$(cd $(dirname $0)/..; pwd)

echo "Server starting........................" >>applog/start.log
####### ads 启动时 添加的java agent等设置，如RASP 这一部分 最好不要修改 start
REST_PORT=8003
DSF_PORT=7003
export REST_PORT DSF_PORT
start_opts="$JAVA_OPTS"
start_opts="$start_opts $CATALINA_OPTS"
# ads 默认服务端口8003
start_opts="$start_opts -Dserver.port=${REST_PORT}"
####### ads 启动时 添加的java agent等设置，如RASP 这一部分 最好不要修改 end

####### 对接HIS应用配置中心启动参数，此为测试环境配置参数，生产环境需额外修改  修改 start
# 选取对应环境的配置文件进行部署

####### 对接HIS应用配置中心启动参数，此为测试环境配置参数，生产环境需额外修改  修改 end

####### 如果需要添加其他自定义变量设置 可以在这里添加 修改 start
start_opts="$start_opts -XX:MaxMetaspaceSize=512m"
## 启动jar包设置 和编译脚本对应
start_opts="$start_opts -jar $BASE_DIR/libs/app.jar --spring.profiles.active=${DEPLOY_ENV:-"test"}"
####### 如果需要添加其他自定义变量设置 可以在这里添加 修改 end
vm_port=$(echo "$VMPORT" | sed -n 's/.*{"originport":"8003"[^}]*"HostPort":"\([0-9]*\)"[^}]*}.*/\1/p')
start_opts="$start_opts -Deureka.instance.ip-address=$VMIP"
start_opts="$start_opts -Deureka.instance.non-secure-port=${vm_port}"
# 可以不修改
echo "Dist Dir=========$BASE_DIR" >> applog/start.log
echo "JAVA_OPTS=========$JAVA_OPTS" >> applog/start.log
echo "param1=========$1" >> applog/start.log
echo "CATALINA_OPTS=========$CATALINA_OPTS" >> applog/start.log
echo "start execute........cmd:========java $start_opts" >> applog/start.log

# 启动指令
java $start_opts